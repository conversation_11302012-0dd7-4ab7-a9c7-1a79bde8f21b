# targets to analyse
tabby.build.target                        = /Users/<USER>/Documents/codes/java/tabby/cases/CrushFTP_11.3.0_3.jar
#tabby.build.target                        = cases/iserver-all-12.0.0-24626.jar
#tabby.build.target                        = cases/java-sec-code-1.0.0.jar
#tabby.build.target                        = /Users/<USER>/Documents/codes/java/benchmark/target/benchmark-1.0-SNAPSHOT.jar
#tabby.build.target                        = /Users/<USER>/Documents/codes/github/ant-application-security-testing-benchmark/sast-java/target/sast-java-2.7.2.jar
tabby.build.libraries                     = libs
tabby.build.mode                          = web
#tabby.build.mode                          = gadget
tabby.output.directory                    = ./output/dev
tabby.build.rules.directory               = ./rules
tabby.build.thread.size                   = max

# settings for jre environments
tabby.build.useSettingJRE                 = false
tabby.build.isJRE9Module                  = false
#tabby.build.javaHome                      = /Library/Java/JavaVirtualMachines/graalvm-jdk-17.0.9+11.1/Contents/Home
#tabby.build.javaHome                      = /Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home
#tabby.build.javaHome                      = /Library/Java/JavaVirtualMachines/zulu-21.jdk/Contents/Home
tabby.build.javaHome                      = /Library/Java/JavaVirtualMachines/zulu-8.jdk/Contents/Home

# debug
tabby.debug.details                       = false
tabby.debug.print.current.methods         = true

# jdk settings
tabby.build.isJDKProcess                  = false
tabby.build.withAllJDK                    = false
tabby.build.isJDKOnly                     = false

# dealing fatjar
tabby.build.checkFatJar                   = true

# set false for debug
tabby.build.removeNotPollutedCallSite     = false

# pointed-to analysis types
tabby.build.interProcedural               = true
tabby.build.onDemandDrive                 = false
#tabby.build.pointer.algorithm.type        = SimpleTypeAnalysis
tabby.build.pointer.algorithm.type        = CachedPointerAnalysis
# set RTA on, And the mode is not "gadget". ?????????????????interface??????????????
tabby.build.call.graph.rta.enable         = false

# pointed-to analysis settings
tabby.build.analysis.everything           = true
tabby.build.isPrimTypeNeedToCreate        = true
tabby.build.thread.timeout                = 2
tabby.build.method.timeout                = 5
tabby.build.alias.maxCount                = 5
tabby.build.array.maxLength               = 100
tabby.build.method.maxDepth               = 500
tabby.build.method.maxBodyCount           = 8000
tabby.build.object.maxTriggerTimes        = 300
tabby.build.object.field.k.limit          = 10
tabby.build.summaries.size.limit          = 50
tabby.build.with.cache.enable             = false
tabby.build.isNeedToCreateIgnoreList      = false
tabby.build.isNeedToDealNewAddedMethod    = true
tabby.build.timeout.forceStop             = true

# plugin settings
tabby.build.isNeedToProcessXml            = true