package tabby.analysis.v1.switcher;


import soot.jimple.Constant;
import tabby.analysis.v1.container.ValueContainer;
import tabby.analysis.v1.data.Context;

/**
 * 简单的变量处理器
 * 用于处理Local、StaticFieldRef、InstanceFieldRef、ArrayRef
 * Context.getOrAdd
 * 不关注
 */
public class ValueParser extends ValueSwitcher{

    public ValueParser(ValueContainer container, boolean ifNotExistOrNew) {
        this.container = container;
        this.ifNotExistOrNew = ifNotExistOrNew;
    }

    public void accept(Context context) {
        accept(context.getContainer());
    }

    public void accept(ValueContainer container) {
        this.container = container;
    }

    @Override
    public void defaultCase(Object v) {
        if (v instanceof Constant) {
            caseConstant((Constant) v);
        }
    }

}
