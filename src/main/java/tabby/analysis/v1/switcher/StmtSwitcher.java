package tabby.analysis.v1.switcher;

import com.google.common.collect.Multimap;
import soot.jimple.AbstractStmtSwitch;
import tabby.analysis.v1.data.Context;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/1/8
 */
public abstract class StmtSwitcher extends AbstractStmtSwitch {
    protected Context context;
    protected ValueSwitcher valueSwitcher;
    protected Map<String, Integer> triggerTimes;
    public Multimap<String, String> actions = null;

    public abstract void accept(Context context);

    public void setTriggerTimes(Map<String, Integer> triggerTimes) {
        this.triggerTimes = triggerTimes;
    }

    public void setActions(Multimap<String, String> actions) {
        this.actions = actions;
    }
}
