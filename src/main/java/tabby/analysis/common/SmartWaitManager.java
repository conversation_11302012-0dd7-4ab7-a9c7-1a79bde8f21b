package tabby.analysis.common;

import lombok.extern.slf4j.Slf4j;
import tabby.config.GlobalConfiguration;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 智能等待管理器
 * 解决多个线程等待一个线程完成分析的问题
 * 
 * <AUTHOR>
 * @since 2024/01/01
 */
@Slf4j
public class SmartWaitManager {
    
    // 每个方法的等待线程计数
    private static final ConcurrentHashMap<String, AtomicInteger> waitingThreads = new ConcurrentHashMap<>();
    
    // 每个方法的分析开始时间
    private static final ConcurrentHashMap<String, AtomicLong> analysisStartTime = new ConcurrentHashMap<>();
    
    // 全局统计
    private static final AtomicInteger totalWaitingThreads = new AtomicInteger(0);
    private static final AtomicLong totalWaitTime = new AtomicLong(0);
    private static final AtomicInteger rejectedWaits = new AtomicInteger(0);
    
    /**
     * 等待决策结果
     */
    public enum WaitDecision {
        WAIT,           // 等待
        SKIP_WAIT,      // 跳过等待
        FORCE_ANALYZE   // 强制分析
    }
    
    /**
     * 等待结果
     */
    public static class WaitResult {
        private final WaitDecision decision;
        private final long waitTimeMs;
        private final String reason;
        
        public WaitResult(WaitDecision decision, long waitTimeMs, String reason) {
            this.decision = decision;
            this.waitTimeMs = waitTimeMs;
            this.reason = reason;
        }
        
        public WaitDecision getDecision() { return decision; }
        public long getWaitTimeMs() { return waitTimeMs; }
        public String getReason() { return reason; }
    }
    
    /**
     * 决定是否等待以及等待时间
     * 
     * @param methodSignature 方法签名
     * @return 等待决策
     */
    public static WaitResult decideWait(String methodSignature) {
        AnalysisConfiguration config = AnalysisConfiguration.getInstance();
        
        // 1. 检查当前等待线程数
        AtomicInteger waitCount = waitingThreads.computeIfAbsent(methodSignature, k -> new AtomicInteger(0));
        int currentWaiters = waitCount.get();
        
        if (currentWaiters >= config.getMaxConcurrentWaiters()) {
            rejectedWaits.incrementAndGet();
            return new WaitResult(WaitDecision.SKIP_WAIT, 0, 
                "Too many waiters: " + currentWaiters);
        }
        
        // 2. 检查分析已运行时间
        AtomicLong startTime = analysisStartTime.get(methodSignature);
        if (startTime != null) {
            long elapsedMs = System.currentTimeMillis() - startTime.get();
            long methodTimeoutMs = GlobalConfiguration.METHOD_TIMEOUT * 60 * 1000L;
            
            // 如果已经运行了很长时间，建议跳过等待
            if (elapsedMs > methodTimeoutMs * 0.8) {
                return new WaitResult(WaitDecision.SKIP_WAIT, 0, 
                    "Analysis running too long: " + elapsedMs + "ms");
            }
            
            // 如果刚开始分析，给更短的等待时间
            if (elapsedMs < 1000) {
                long waitTime = config.getAdaptiveWaitBaseMs();
                return new WaitResult(WaitDecision.WAIT, waitTime, 
                    "Analysis just started, short wait");
            }
        }
        
        // 3. 自适应等待时间计算
        long baseWaitTime = config.getWaitTimeoutMs();
        
        // 根据当前等待线程数调整等待时间
        long adjustedWaitTime = baseWaitTime / (currentWaiters + 1);
        
        // 确保在合理范围内
        adjustedWaitTime = Math.max(adjustedWaitTime, config.getAdaptiveWaitBaseMs());
        adjustedWaitTime = Math.min(adjustedWaitTime, config.getMaxWaitTimeoutMs());
        
        return new WaitResult(WaitDecision.WAIT, adjustedWaitTime, 
            "Adaptive wait with " + currentWaiters + " other waiters");
    }
    
    /**
     * 开始等待
     * 
     * @param methodSignature 方法签名
     */
    public static void startWaiting(String methodSignature) {
        waitingThreads.computeIfAbsent(methodSignature, k -> new AtomicInteger(0)).incrementAndGet();
        totalWaitingThreads.incrementAndGet();
        
        log.debug("Thread {} started waiting for method {}", 
            Thread.currentThread().getName(), methodSignature);
    }
    
    /**
     * 结束等待
     * 
     * @param methodSignature 方法签名
     * @param actualWaitTimeMs 实际等待时间
     */
    public static void endWaiting(String methodSignature, long actualWaitTimeMs) {
        AtomicInteger waitCount = waitingThreads.get(methodSignature);
        if (waitCount != null) {
            int remaining = waitCount.decrementAndGet();
            if (remaining <= 0) {
                waitingThreads.remove(methodSignature);
            }
        }
        
        totalWaitingThreads.decrementAndGet();
        totalWaitTime.addAndGet(actualWaitTimeMs);
        
        log.debug("Thread {} finished waiting for method {}, waited {}ms", 
            Thread.currentThread().getName(), methodSignature, actualWaitTimeMs);
    }
    
    /**
     * 记录分析开始
     * 
     * @param methodSignature 方法签名
     */
    public static void recordAnalysisStart(String methodSignature) {
        analysisStartTime.put(methodSignature, new AtomicLong(System.currentTimeMillis()));
        
        log.debug("Analysis started for method {}", methodSignature);
    }
    
    /**
     * 记录分析结束
     * 
     * @param methodSignature 方法签名
     */
    public static void recordAnalysisEnd(String methodSignature) {
        AtomicLong startTime = analysisStartTime.remove(methodSignature);
        if (startTime != null) {
            long duration = System.currentTimeMillis() - startTime.get();
            log.debug("Analysis completed for method {} in {}ms", methodSignature, duration);
        }
        
        // 清理等待线程计数（防止内存泄漏）
        waitingThreads.remove(methodSignature);
    }
    
    /**
     * 获取等待统计信息
     */
    public static WaitStatistics getStatistics() {
        return new WaitStatistics(
            totalWaitingThreads.get(),
            waitingThreads.size(),
            totalWaitTime.get(),
            rejectedWaits.get()
        );
    }
    
    /**
     * 清理所有状态
     */
    public static void clearAll() {
        waitingThreads.clear();
        analysisStartTime.clear();
        totalWaitingThreads.set(0);
        totalWaitTime.set(0);
        rejectedWaits.set(0);
        
        log.info("SmartWaitManager cleared all state");
    }
    
    /**
     * 获取方法的当前等待线程数
     */
    public static int getWaitingThreadCount(String methodSignature) {
        AtomicInteger count = waitingThreads.get(methodSignature);
        return count != null ? count.get() : 0;
    }
    
    /**
     * 检查是否应该强制开始新的分析
     * 当等待线程过多或分析时间过长时，可能需要强制开始新的分析
     */
    public static boolean shouldForceNewAnalysis(String methodSignature) {
        AnalysisConfiguration config = AnalysisConfiguration.getInstance();
        
        // 检查等待线程数
        int waiters = getWaitingThreadCount(methodSignature);
        if (waiters > config.getMaxConcurrentWaiters() * 2) {
            log.warn("Too many waiters ({}) for method {}, suggesting force analysis", 
                waiters, methodSignature);
            return true;
        }
        
        // 检查分析运行时间
        AtomicLong startTime = analysisStartTime.get(methodSignature);
        if (startTime != null) {
            long elapsedMs = System.currentTimeMillis() - startTime.get();
            long methodTimeoutMs = GlobalConfiguration.METHOD_TIMEOUT * 60 * 1000L;
            
            if (elapsedMs > methodTimeoutMs) {
                log.warn("Analysis running too long ({}ms) for method {}, suggesting force analysis", 
                    elapsedMs, methodSignature);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 等待统计信息
     */
    public static class WaitStatistics {
        private final int currentWaitingThreads;
        private final int methodsWithWaiters;
        private final long totalWaitTimeMs;
        private final int rejectedWaits;
        
        public WaitStatistics(int currentWaitingThreads, int methodsWithWaiters, 
                            long totalWaitTimeMs, int rejectedWaits) {
            this.currentWaitingThreads = currentWaitingThreads;
            this.methodsWithWaiters = methodsWithWaiters;
            this.totalWaitTimeMs = totalWaitTimeMs;
            this.rejectedWaits = rejectedWaits;
        }
        
        public int getCurrentWaitingThreads() { return currentWaitingThreads; }
        public int getMethodsWithWaiters() { return methodsWithWaiters; }
        public long getTotalWaitTimeMs() { return totalWaitTimeMs; }
        public int getRejectedWaits() { return rejectedWaits; }
        
        public double getAverageWaitTimeMs() {
            return rejectedWaits > 0 ? (double) totalWaitTimeMs / rejectedWaits : 0.0;
        }
        
        @Override
        public String toString() {
            return String.format(
                "WaitStatistics{currentWaiters=%d, methodsWithWaiters=%d, totalWaitTime=%dms, rejectedWaits=%d, avgWaitTime=%.1fms}",
                currentWaitingThreads, methodsWithWaiters, totalWaitTimeMs, rejectedWaits, getAverageWaitTimeMs()
            );
        }
    }
}
