package tabby.analysis.common;

import lombok.extern.slf4j.Slf4j;
import soot.Body;
import soot.SootMethod;
import tabby.analysis.v2.CachedPointerAnalysis;
import tabby.analysis.v2.data.Context;
import tabby.common.bean.ref.MethodReference;
import tabby.common.utils.SemanticUtils;
import tabby.config.GlobalConfiguration;

/**
 * 统一的分析管理器
 * 集成冲突解决功能，递归检测由Context处理
 *
 * <AUTHOR>
 * @since 2024/01/01
 */
@Slf4j
public class AnalysisManager {
    
    /**
     * 分析方法的统一入口
     *
     * @param method Soot方法
     * @param context 分析上下文
     * @return 是否成功分析
     */
    public static boolean analyzeMethod(SootMethod method, Context context) {
        MethodReference methodReference = context.getMethodReference();
        String methodSignature = methodReference.getSignature();

        // 1. 检查基本条件
        if (methodReference.isBodyParseError()) {
            log.debug("Method {} has body parse error, skipping", methodSignature);
            return false;
        }

        // 2. 检查分析冲突
        AnalysisConflictResolver.AnalysisResult conflictResult =
            AnalysisConflictResolver.tryStartAnalysis(methodReference);

        switch (conflictResult.getType()) {
            case ALREADY_COMPLETED:
                log.debug("Method {} already completed", methodSignature);
                return true;

            case COMPLETED_BY_OTHER:
                log.debug("Method {} completed by other thread", methodSignature);
                return true;

            case RECURSIVE_CALL:
                log.debug("Recursive call detected for method {}", methodSignature);
                return false;

            case WAIT_TIMEOUT:
                log.warn("Timeout waiting for method {} analysis", methodSignature);
                return false;

            case CAN_PROCEED:
            case CAN_RETRY:
            case FORCE_PROCEED:
                // 可以继续分析
                return performActualAnalysis(method, context);

            default:
                log.warn("Unknown conflict result type: {}", conflictResult.getType());
                return false;
        }
    }
    
    /**
     * 执行实际的方法分析
     */
    private static boolean performActualAnalysis(SootMethod method, Context context) {
        MethodReference methodReference = context.getMethodReference();
        String methodSignature = methodReference.getSignature();
        
        try {
            // 获取方法体
            Body body = SemanticUtils.retrieveBody(method, methodSignature, true);

            // 检查方法体大小
            if (isMethodTooLarge(body, methodSignature)) {
                methodReference.setInitialed(true);
                methodReference.setActionInitialed(true);
                AnalysisConflictResolver.completeAnalysis(methodReference, true);
                return false;
            }

            // 执行分析
            boolean success = CachedPointerAnalysis.processMethodInternal(body, context);

            // 更新分析状态
            AnalysisConflictResolver.completeAnalysis(methodReference, success);
            
            return success;
            
        } catch (Exception e) {
            log.error("Error analyzing method {}: {}", methodSignature, e.getMessage(), e);
            
            // 处理异常情况
            handleAnalysisException(methodReference, methodSignature, e);
            AnalysisConflictResolver.completeAnalysis(methodReference, false);
            
            return false;
        }
    }
    
    /**
     * 检查方法是否过大
     */
    private static boolean isMethodTooLarge(Body body, String methodSignature) {
        try {
            if(body != null){
                int bodySize = body.getUnits().size();
                if (bodySize >= GlobalConfiguration.METHOD_MAX_BODY_COUNT) {
                    log.debug("Method {} body is too big ({}), ignoring", methodSignature, bodySize);
                    return true;
                }
            }
        } catch (Exception e) {
            log.debug("Cannot check method body size for {}: {}", methodSignature, e.getMessage());
        }
        return false;
    }
    
    /**
     * 处理分析异常
     */
    private static void handleAnalysisException(MethodReference methodReference, 
                                              String methodSignature, Exception e) {
        String msg = e.getMessage();
        if (msg != null && msg.contains("Body retrieve error: ")) {
            methodReference.setBodyParseError(true);
            log.warn("Body retrieve error for method {}: {}", methodSignature, msg);
        } else {
            log.error("Unexpected error analyzing method {}: {}", methodSignature, msg, e);
        }
    }
    
    /**
     * 使用自动资源管理的方法分析
     * 简化版本，递归检测由Context处理
     *
     * @param method Soot方法
     * @param context 分析上下文
     * @return 是否成功分析
     */
    public static boolean analyzeMethodWithAutoManagement(SootMethod method, Context context) {
        MethodReference methodReference = context.getMethodReference();
        String methodSignature = methodReference.getSignature();

        // 检查基本条件
        if (methodReference.isBodyParseError()) {
            log.debug("Method {} has body parse error, skipping", methodSignature);
            return false;
        }

        // 检查分析冲突并执行分析
        AnalysisConflictResolver.AnalysisResult conflictResult =
            AnalysisConflictResolver.tryStartAnalysis(methodReference);

        switch (conflictResult.getType()) {
            case ALREADY_COMPLETED:
            case COMPLETED_BY_OTHER:
                return true;

            case RECURSIVE_CALL:
            case WAIT_TIMEOUT:
                return false;

            case CAN_PROCEED:
            case CAN_RETRY:
            case FORCE_PROCEED:
                return performActualAnalysis(method, context);

            default:
                return false;
        }
    }
    
    /**
     * 清理所有分析状态
     */
    public static void cleanup() {
        AnalysisConflictResolver.clearAllTasks();
        log.info("Analysis manager cleanup completed");
    }
    
    /**
     * 获取分析统计信息 - 极简版本
     */
    public static AnalysisStatistics getStatistics() {
        return new AnalysisStatistics(
            AnalysisConflictResolver.getActiveTaskCount()
        );
    }
    
    /**
     * 分析统计信息
     */
    public static class AnalysisStatistics {
        private final int activeTaskCount;

        public AnalysisStatistics(int activeTaskCount) {
            this.activeTaskCount = activeTaskCount;
        }

        public int getActiveTaskCount() {
            return activeTaskCount;
        }

        @Override
        public String toString() {
            return String.format("AnalysisStatistics{activeTasks=%d}", activeTaskCount);
        }
    }
}
