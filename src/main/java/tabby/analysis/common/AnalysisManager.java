package tabby.analysis.common;

import lombok.extern.slf4j.Slf4j;
import soot.SootMethod;
import tabby.analysis.v2.CachedPointerAnalysis;
import tabby.analysis.v2.data.Context;
import tabby.common.bean.ref.MethodReference;
import tabby.config.GlobalConfiguration;

/**
 * 统一的分析管理器
 * 集成冲突解决和递归检测功能
 * 
 * <AUTHOR>
 * @since 2024/01/01
 */
@Slf4j
public class AnalysisManager {
    
    /**
     * 分析方法的统一入口
     * 
     * @param method Soot方法
     * @param context 分析上下文
     * @return 是否成功分析
     */
    public static boolean analyzeMethod(SootMethod method, Context context) {
        MethodReference methodReference = context.getMethodReference();
        String methodSignature = methodReference.getSignature();
        
        // 1. 检查基本条件
        if (methodReference.isBodyParseError()) {
            log.debug("Method {} has body parse error, skipping", methodSignature);
            return false;
        }
        
        // 2. 检查递归
        RecursionDetector.RecursionCheckResult recursionCheck = 
            RecursionDetector.enterMethod(methodSignature);
        
        if (!recursionCheck.canProceed()) {
            log.debug("Cannot analyze method {} due to recursion: {}", 
                methodSignature, recursionCheck.getReason());
            return false;
        }
        
        try {
            // 3. 检查分析冲突
            AnalysisConflictResolver.AnalysisResult conflictResult = 
                AnalysisConflictResolver.tryStartAnalysis(methodSignature, methodReference);
            
            switch (conflictResult.getType()) {
                case ALREADY_COMPLETED:
                    log.debug("Method {} already completed", methodSignature);
                    return true;
                    
                case COMPLETED_BY_OTHER:
                    log.debug("Method {} completed by other thread", methodSignature);
                    return true;
                    
                case RECURSIVE_CALL:
                    log.debug("Recursive call detected for method {}", methodSignature);
                    return false;
                    
                case WAIT_TIMEOUT:
                    log.warn("Timeout waiting for method {} analysis", methodSignature);
                    return false;
                    
                case CAN_PROCEED:
                case CAN_RETRY:
                    // 可以继续分析
                    return performActualAnalysis(method, context, conflictResult.getTask());
                    
                default:
                    log.warn("Unknown conflict result type: {}", conflictResult.getType());
                    return false;
            }
            
        } finally {
            // 4. 退出递归检测
            RecursionDetector.exitMethod(methodSignature);
        }
    }
    
    /**
     * 执行实际的方法分析
     */
    private static boolean performActualAnalysis(SootMethod method, Context context, 
                                               AnalysisConflictResolver.AnalysisTask task) {
        MethodReference methodReference = context.getMethodReference();
        String methodSignature = methodReference.getSignature();
        
        try {
            // 检查方法体大小
            if (isMethodTooLarge(method, methodSignature)) {
                methodReference.setInitialed(true);
                methodReference.setActionInitialed(true);
                AnalysisConflictResolver.completeAnalysis(methodSignature, 
                    AnalysisConflictResolver.AnalysisState.COMPLETED);
                return false;
            }
            
            // 执行分析
            boolean success = CachedPointerAnalysis.processMethod(method, context);
            
            // 更新分析状态
            AnalysisConflictResolver.AnalysisState finalState = success ? 
                AnalysisConflictResolver.AnalysisState.COMPLETED : 
                AnalysisConflictResolver.AnalysisState.FAILED;
                
            AnalysisConflictResolver.completeAnalysis(methodSignature, finalState);
            
            return success;
            
        } catch (Exception e) {
            log.error("Error analyzing method {}: {}", methodSignature, e.getMessage(), e);
            
            // 处理异常情况
            handleAnalysisException(methodReference, methodSignature, e);
            AnalysisConflictResolver.completeAnalysis(methodSignature, 
                AnalysisConflictResolver.AnalysisState.FAILED);
            
            return false;
        }
    }
    
    /**
     * 检查方法是否过大
     */
    private static boolean isMethodTooLarge(SootMethod method, String methodSignature) {
        try {
            if (method.hasActiveBody()) {
                int bodySize = method.getActiveBody().getUnits().size();
                if (bodySize >= GlobalConfiguration.METHOD_MAX_BODY_COUNT) {
                    log.debug("Method {} body is too big ({}), ignoring", methodSignature, bodySize);
                    return true;
                }
            }
        } catch (Exception e) {
            log.debug("Cannot check method body size for {}: {}", methodSignature, e.getMessage());
        }
        return false;
    }
    
    /**
     * 处理分析异常
     */
    private static void handleAnalysisException(MethodReference methodReference, 
                                              String methodSignature, Exception e) {
        String msg = e.getMessage();
        if (msg != null && msg.contains("Body retrieve error: ")) {
            methodReference.setBodyParseError(true);
            log.warn("Body retrieve error for method {}: {}", methodSignature, msg);
        } else {
            log.error("Unexpected error analyzing method {}: {}", methodSignature, msg, e);
        }
    }
    
    /**
     * 使用自动资源管理的方法分析
     * 推荐使用这个方法，它会自动处理递归检测的进入和退出
     * 
     * @param method Soot方法
     * @param context 分析上下文
     * @return 是否成功分析
     */
    public static boolean analyzeMethodWithAutoManagement(SootMethod method, Context context) {
        MethodReference methodReference = context.getMethodReference();
        String methodSignature = methodReference.getSignature();
        
        // 使用自动资源管理
        try (RecursionDetector.MethodAnalysisContext recursionContext = 
                 RecursionDetector.MethodAnalysisContext.create(methodSignature)) {
            
            if (!recursionContext.isEntered()) {
                log.debug("Cannot enter method analysis context for {}", methodSignature);
                return false;
            }
            
            // 检查基本条件
            if (methodReference.isBodyParseError()) {
                log.debug("Method {} has body parse error, skipping", methodSignature);
                return false;
            }
            
            // 检查分析冲突并执行分析
            AnalysisConflictResolver.AnalysisResult conflictResult = 
                AnalysisConflictResolver.tryStartAnalysis(methodSignature, methodReference);
            
            switch (conflictResult.getType()) {
                case ALREADY_COMPLETED:
                case COMPLETED_BY_OTHER:
                    return true;
                    
                case RECURSIVE_CALL:
                case WAIT_TIMEOUT:
                    return false;
                    
                case CAN_PROCEED:
                case CAN_RETRY:
                    return performActualAnalysis(method, context, conflictResult.getTask());
                    
                default:
                    return false;
            }
        }
    }
    
    /**
     * 清理所有分析状态
     */
    public static void cleanup() {
        AnalysisConflictResolver.clearAllTasks();
        RecursionDetector.clearAll();
        log.info("Analysis manager cleanup completed");
    }
    
    /**
     * 获取分析统计信息
     */
    public static AnalysisStatistics getStatistics() {
        return new AnalysisStatistics(
            AnalysisConflictResolver.getActiveTaskCount(),
            RecursionDetector.getRegisteredMethodCount(),
            RecursionDetector.getCurrentDepth()
        );
    }
    
    /**
     * 分析统计信息
     */
    public static class AnalysisStatistics {
        private final int activeTaskCount;
        private final int registeredMethodCount;
        private final int currentDepth;
        
        public AnalysisStatistics(int activeTaskCount, int registeredMethodCount, int currentDepth) {
            this.activeTaskCount = activeTaskCount;
            this.registeredMethodCount = registeredMethodCount;
            this.currentDepth = currentDepth;
        }
        
        public int getActiveTaskCount() {
            return activeTaskCount;
        }
        
        public int getRegisteredMethodCount() {
            return registeredMethodCount;
        }
        
        public int getCurrentDepth() {
            return currentDepth;
        }
        
        @Override
        public String toString() {
            return String.format("AnalysisStatistics{activeTasks=%d, registeredMethods=%d, currentDepth=%d}", 
                activeTaskCount, registeredMethodCount, currentDepth);
        }
    }
}
