package tabby.analysis.common;

import lombok.extern.slf4j.Slf4j;
import tabby.common.bean.ref.MethodReference;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 轻量级函数分析冲突解决器
 * 基于MethodReference唯一性的状态管理
 * 
 * <AUTHOR>
 * @since 2024/01/01
 */
@Slf4j
public class AnalysisConflictResolver {
    
    /**
     * 分析状态枚举
     */
    public enum AnalysisState {
        NOT_STARTED,    // 未开始
        IN_PROGRESS,    // 进行中
        COMPLETED,      // 已完成
        FAILED,         // 失败
        TIMEOUT         // 超时
    }
    
    /**
     * 分析结果类型
     */
    public enum AnalysisResultType {
        CAN_PROCEED,        // 可以继续分析
        ALREADY_COMPLETED,  // 已经完成
        COMPLETED_BY_OTHER, // 被其他线程完成
        RECURSIVE_CALL,     // 递归调用
        WAIT_TIMEOUT,       // 等待超时
        CAN_RETRY,          // 可以重试
        FORCE_PROCEED       // 强制继续（等待线程过多）
    }
    
    /**
     * 分析结果
     */
    public static class AnalysisResult {
        private final AnalysisResultType type;
        private final String reason;
        
        public AnalysisResult(AnalysisResultType type, String reason) {
            this.type = type;
            this.reason = reason;
        }
        
        public AnalysisResultType getType() {
            return type;
        }
        
        public String getReason() {
            return reason;
        }
        
        public boolean shouldProceed() {
            return type == AnalysisResultType.CAN_PROCEED || 
                   type == AnalysisResultType.CAN_RETRY ||
                   type == AnalysisResultType.FORCE_PROCEED;
        }
        
        public boolean shouldSkip() {
            return type == AnalysisResultType.ALREADY_COMPLETED || 
                   type == AnalysisResultType.COMPLETED_BY_OTHER ||
                   type == AnalysisResultType.RECURSIVE_CALL;
        }
    }
    
    // 配置参数
    private static final int MAX_CONCURRENT_WAITERS = 3; // 最大并发等待线程数
    
    /**
     * 尝试开始分析指定方法
     * 
     * @param methodReference 方法引用
     * @return AnalysisResult 分析结果
     */
    public static AnalysisResult tryStartAnalysis(MethodReference methodReference) {
        // 快速检查：如果方法已经分析完成，直接返回
        if (methodReference.isActionInitialed()) {
            return new AnalysisResult(AnalysisResultType.ALREADY_COMPLETED, "Already completed");
        }
        
        // 检查并发等待线程数
        int currentWaiters = methodReference.incrementWaitingThreads();
        
        try {
            // 如果等待线程过多，强制开始新的分析
            if (currentWaiters > MAX_CONCURRENT_WAITERS) {
                log.debug("Too many waiting threads ({}), force proceeding for method: {}", 
                    currentWaiters, methodReference.getSignature());
                return new AnalysisResult(AnalysisResultType.FORCE_PROCEED, 
                    "Too many waiters: " + currentWaiters);
            }
            
            // 尝试获取分析权限
            if (methodReference.tryStartAnalysis()) {
                // 成功获取分析权限，可以开始分析
                return new AnalysisResult(AnalysisResultType.CAN_PROCEED, "Analysis started");
            }
            
            // 其他线程正在分析，等待完成
            return waitForAnalysisCompletion(methodReference);
            
        } finally {
            // 减少等待线程计数
            methodReference.decrementWaitingThreads();
        }
    }
    
    /**
     * 等待其他线程完成分析
     */
    private static AnalysisResult waitForAnalysisCompletion(MethodReference methodReference) {
        // 检查是否是同一个线程的递归调用
        if (Thread.currentThread().equals(methodReference.getAnalysisOwnerThread())) {
            return new AnalysisResult(AnalysisResultType.RECURSIVE_CALL, "Recursive call detected");
        }
        
        // 等待分析完成
        long waitTime = AnalysisConfiguration.getInstance().getWaitTimeoutMs();
        boolean completed = methodReference.waitForAnalysisCompletion(waitTime);
        
        if (!completed) {
            log.debug("Timeout waiting for analysis completion: {}", methodReference.getSignature());
            return new AnalysisResult(AnalysisResultType.WAIT_TIMEOUT, "Wait timeout");
        }
        
        // 检查分析结果
        AnalysisState finalState = methodReference.getAnalysisState();
        if (finalState == AnalysisState.COMPLETED) {
            // 其他线程已完成分析，由于MethodReference是唯一的，结果已经在对象中
            return new AnalysisResult(AnalysisResultType.COMPLETED_BY_OTHER, "Completed by other thread");
        } else {
            // 其他线程分析失败，当前线程可以重试
            return new AnalysisResult(AnalysisResultType.CAN_RETRY, "Other thread failed");
        }
    }
    
    /**
     * 完成分析
     */
    public static void completeAnalysis(MethodReference methodReference, boolean success) {
        methodReference.completeAnalysis(success);
    }
    
    /**
     * 获取活跃任务数量（用于监控）
     */
    public static int getActiveTaskCount() {
        // 由于不再维护全局任务列表，返回0
        // 实际的活跃任务数可以通过其他方式统计
        return 0;
    }
    
    /**
     * 清理所有任务（用于系统关闭）
     */
    public static void clearAllTasks() {
        // 由于状态都在MethodReference中，这里不需要特殊处理
        log.info("AnalysisConflictResolver cleanup completed");
    }
}
