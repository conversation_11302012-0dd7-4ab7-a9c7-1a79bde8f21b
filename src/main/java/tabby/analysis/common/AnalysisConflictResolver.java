package tabby.analysis.common;

import lombok.extern.slf4j.Slf4j;
import tabby.common.bean.ref.MethodReference;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 高效的函数分析冲突解决器
 * 解决多线程环境下同一函数被重复分析的问题
 * 
 * <AUTHOR>
 * @since 2024/01/01
 */
@Slf4j
public class AnalysisConflictResolver {
    
    /**
     * 分析状态枚举
     */
    public enum AnalysisState {
        NOT_STARTED,    // 未开始
        IN_PROGRESS,    // 进行中
        COMPLETED,      // 已完成
        FAILED,         // 失败
        TIMEOUT         // 超时
    }
    
    /**
     * 分析任务信息
     */
    public static class AnalysisTask {
        private final String methodSignature;
        private final AtomicReference<AnalysisState> state;
        private final CountDownLatch completionLatch;
        private final long startTime;
        private volatile Thread ownerThread;

        public AnalysisTask(String methodSignature) {
            this.methodSignature = methodSignature;
            this.state = new AtomicReference<>(AnalysisState.NOT_STARTED);
            this.completionLatch = new CountDownLatch(1);
            this.startTime = System.currentTimeMillis();
        }

        public boolean tryStart(Thread thread, MethodReference methodRef) {
            if (state.compareAndSet(AnalysisState.NOT_STARTED, AnalysisState.IN_PROGRESS)) {
                this.ownerThread = thread;
                methodRef.setRunning(true);
                return true;
            }
            return false;
        }

        public void complete(AnalysisState finalState, MethodReference methodRef) {
            if (state.compareAndSet(AnalysisState.IN_PROGRESS, finalState)) {
                if (methodRef != null) {
                    methodRef.setRunning(false);
                    if (finalState == AnalysisState.COMPLETED) {
                        methodRef.setInitialed(true);
                        methodRef.setActionInitialed(true);
                    }
                }
                completionLatch.countDown();
            }
        }
        
        public boolean waitForCompletion(long timeoutMs) {
            try {
                return completionLatch.await(timeoutMs, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        
        public AnalysisState getState() {
            return state.get();
        }
        
        public boolean isCompleted() {
            AnalysisState currentState = state.get();
            return currentState == AnalysisState.COMPLETED || 
                   currentState == AnalysisState.FAILED || 
                   currentState == AnalysisState.TIMEOUT;
        }
        
        public long getElapsedTime() {
            return System.currentTimeMillis() - startTime;
        }
        
        public Thread getOwnerThread() {
            return ownerThread;
        }
    }
    
    // 轻量级任务注册表 - 只存储正在运行的任务，限制大小
    private static final ConcurrentHashMap<String, AnalysisTask> activeTasks = new ConcurrentHashMap<>();
    private static final int MAX_ACTIVE_TASKS = 1000; // 限制最大活跃任务数
    
    // 配置参数
    private static AnalysisConfiguration getConfig() {
        return AnalysisConfiguration.getInstance();
    }
    
    /**
     * 尝试开始分析指定方法
     * 
     * @param methodSignature 方法签名
     * @param methodReference 方法引用
     * @return AnalysisResult 分析结果
     */
    public static AnalysisResult tryStartAnalysis(String methodSignature, MethodReference methodReference) {
        // 快速检查：如果方法已经分析完成，直接返回
        if (methodReference.isActionInitialed()) {
            return new AnalysisResult(AnalysisResultType.ALREADY_COMPLETED, null);
        }

        // 检查活跃任务数量，防止内存溢出
        if (activeTasks.size() >= MAX_ACTIVE_TASKS) {
            // 清理已完成的任务
            cleanupCompletedTasks();

            // 如果清理后仍然超限，直接开始新分析
            if (activeTasks.size() >= MAX_ACTIVE_TASKS) {
                return new AnalysisResult(AnalysisResultType.CAN_PROCEED, null);
            }
        }

        Thread currentThread = Thread.currentThread();

        // 获取或创建分析任务
        AnalysisTask task = activeTasks.computeIfAbsent(methodSignature, AnalysisTask::new);

        // 尝试获取分析权限
        if (task.tryStart(currentThread, methodReference)) {
            // 成功获取分析权限，可以开始分析
            return new AnalysisResult(AnalysisResultType.CAN_PROCEED, task);
        }

        // 其他线程正在分析，简化等待逻辑
        return waitForAnalysisCompletion(task, methodReference);
    }
    
    /**
     * 等待其他线程完成分析 - 简化版本，减少内存使用
     */
    private static AnalysisResult waitForAnalysisCompletion(AnalysisTask task, MethodReference methodReference) {
        // 检查是否是同一个线程的递归调用
        if (Thread.currentThread().equals(task.getOwnerThread())) {
            return new AnalysisResult(AnalysisResultType.RECURSIVE_CALL, null);
        }

        // 简化等待逻辑：固定短时间等待，避免复杂的状态管理
        long waitTime = getConfig().getWaitTimeoutMs();

        try {
            boolean completed = task.waitForCompletion(waitTime);

            if (!completed) {
                // 等待超时，让当前线程重新分析
                return new AnalysisResult(AnalysisResultType.CAN_RETRY, null);
            }

            // 检查分析结果
            AnalysisState finalState = task.getState();
            if (finalState == AnalysisState.COMPLETED) {
                return new AnalysisResult(AnalysisResultType.COMPLETED_BY_OTHER, null);
            } else {
                return new AnalysisResult(AnalysisResultType.CAN_RETRY, null);
            }

        } catch (Exception e) {
            log.debug("Error waiting for analysis completion: {}", e.getMessage());
            return new AnalysisResult(AnalysisResultType.CAN_RETRY, null);
        }
    }
    
    /**
     * 完成分析
     */
    public static void completeAnalysis(String methodSignature, AnalysisState finalState, MethodReference methodRef) {
        AnalysisTask task = activeTasks.get(methodSignature);
        if (task != null) {
            task.complete(finalState, methodRef);

            // 如果分析完成或失败，延迟清理任务
            if (task.isCompleted()) {
                scheduleTaskCleanup(methodSignature, task);
            }
        }
    }
    

    
    /**
     * 立即清理已完成的任务，避免内存泄漏
     */
    private static void cleanupCompletedTasks() {
        activeTasks.entrySet().removeIf(entry -> {
            AnalysisTask task = entry.getValue();
            return task.isCompleted() && task.getElapsedTime() > 60000; // 1分钟后清理
        });
    }

    /**
     * 调度任务清理 - 简化版本
     */
    private static void scheduleTaskCleanup(String methodSignature, AnalysisTask task) {
        // 立即清理，不使用额外线程
        if (task.isCompleted()) {
            activeTasks.remove(methodSignature, task);
        }
    }
    
    /**
     * 清理所有活跃任务（用于系统关闭）
     */
    public static void clearAllTasks() {
        activeTasks.clear();
    }
    
    /**
     * 获取活跃任务数量
     */
    public static int getActiveTaskCount() {
        return activeTasks.size();
    }
    
    /**
     * 分析结果类型
     */
    public enum AnalysisResultType {
        CAN_PROCEED,        // 可以继续分析
        ALREADY_COMPLETED,  // 已经完成
        COMPLETED_BY_OTHER, // 被其他线程完成
        RECURSIVE_CALL,     // 递归调用
        WAIT_TIMEOUT,       // 等待超时
        CAN_RETRY          // 可以重试
    }
    
    /**
     * 分析结果
     */
    public static class AnalysisResult {
        private final AnalysisResultType type;
        private final AnalysisTask task;
        
        public AnalysisResult(AnalysisResultType type, AnalysisTask task) {
            this.type = type;
            this.task = task;
        }
        
        public AnalysisResultType getType() {
            return type;
        }
        
        public AnalysisTask getTask() {
            return task;
        }
        
        public boolean shouldProceed() {
            return type == AnalysisResultType.CAN_PROCEED || type == AnalysisResultType.CAN_RETRY;
        }
        
        public boolean shouldSkip() {
            return type == AnalysisResultType.ALREADY_COMPLETED || 
                   type == AnalysisResultType.COMPLETED_BY_OTHER ||
                   type == AnalysisResultType.RECURSIVE_CALL;
        }
    }
}
