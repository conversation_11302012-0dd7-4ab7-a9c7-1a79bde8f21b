package tabby.analysis.common;

import lombok.extern.slf4j.Slf4j;

/**
 * 分析配置类 - 极简版本
 * 只保留必要的配置参数，避免内存开销
 *
 * <AUTHOR>
 * @since 2024/01/01
 */
@Slf4j
public class AnalysisConfiguration {

    // 单例实例
    private static volatile AnalysisConfiguration instance;
    private static final Object lock = new Object();

    // 极简配置 - 只保留必要参数
    private long waitTimeoutMs = 3000; // 等待超时时间（3秒）

    private AnalysisConfiguration() {
        // 私有构造函数，防止外部实例化
        loadFromSystemProperties();
    }
    
    /**
     * 获取单例实例
     */
    public static AnalysisConfiguration getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new AnalysisConfiguration();
                }
            }
        }
        return instance;
    }
    
    /**
     * 从系统属性加载配置 - 极简版本
     */
    private void loadFromSystemProperties() {
        // 只加载必要配置
        waitTimeoutMs = getLongProperty("tabby.analysis.wait.timeout.ms", waitTimeoutMs);

        logConfiguration();
    }
    
    /**
     * 获取整型系统属性
     */
    private int getIntProperty(String key, int defaultValue) {
        try {
            String value = System.getProperty(key);
            return value != null ? Integer.parseInt(value) : defaultValue;
        } catch (NumberFormatException e) {
            log.warn("Invalid integer value for property {}, using default: {}", key, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 获取长整型系统属性
     */
    private long getLongProperty(String key, long defaultValue) {
        try {
            String value = System.getProperty(key);
            return value != null ? Long.parseLong(value) : defaultValue;
        } catch (NumberFormatException e) {
            log.warn("Invalid long value for property {}, using default: {}", key, defaultValue);
            return defaultValue;
        }
    }

    /**
     * 记录配置信息
     */
    private void logConfiguration() {
        if (log.isInfoEnabled()) {
            log.info("Analysis Configuration:");
            log.info("  Wait timeout: {}ms", waitTimeoutMs);
        }
    }
    
    /**
     * 验证配置的有效性
     */
    public void validate() {
        if (waitTimeoutMs <= 0) {
            throw new IllegalArgumentException("Wait timeout must be positive");
        }
    }
    
    /**
     * 获取等待超时时间
     */
    public long getWaitTimeoutMs() {
        return waitTimeoutMs;
    }

    /**
     * 设置等待超时时间
     */
    public void setWaitTimeoutMs(long waitTimeoutMs) {
        this.waitTimeoutMs = waitTimeoutMs;
    }
    
    /**
     * 重新加载配置
     */
    public void reload() {
        loadFromSystemProperties();
        validate();
        log.info("Analysis configuration reloaded");
    }

    /**
     * 重置为默认配置
     */
    public void resetToDefaults() {
        waitTimeoutMs = 3000;
        log.info("Analysis configuration reset to defaults");
    }

    /**
     * 获取配置摘要
     */
    public String getConfigurationSummary() {
        return String.format(
            "AnalysisConfiguration{waitTimeout=%dms}",
            waitTimeoutMs
        );
    }
}
