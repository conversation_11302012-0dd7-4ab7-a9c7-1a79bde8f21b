package tabby.analysis.common;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

/**
 * 分析配置类
 * 集中管理分析相关的配置参数
 * 
 * <AUTHOR>
 * @since 2024/01/01
 */
@Data
@Slf4j
public class AnalysisConfiguration {
    
    // 单例实例
    private static volatile AnalysisConfiguration instance;
    private static final Object lock = new Object();
    
    // 极简配置 - 只保留必要参数
    private long waitTimeoutMs = 3000; // 等待超时时间（3秒）
    private int maxRecursionDepth = 30; // 最大递归深度（减少到30）
    

    
    private AnalysisConfiguration() {
        // 私有构造函数，防止外部实例化
        loadFromSystemProperties();
    }
    
    /**
     * 获取单例实例
     */
    public static AnalysisConfiguration getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new AnalysisConfiguration();
                }
            }
        }
        return instance;
    }
    
    /**
     * 从系统属性加载配置 - 极简版本
     */
    private void loadFromSystemProperties() {
        // 只加载必要配置
        waitTimeoutMs = getLongProperty("tabby.analysis.wait.timeout.ms", waitTimeoutMs);
        maxRecursionDepth = getIntProperty("tabby.analysis.max.recursion.depth", maxRecursionDepth);
        
        // 性能监控配置
        enableStatistics = getBooleanProperty("tabby.analysis.statistics.enabled", enableStatistics);
        statisticsReportIntervalMs = getLongProperty("tabby.analysis.statistics.report.interval.ms", statisticsReportIntervalMs);
        enablePerformanceLogging = getBooleanProperty("tabby.analysis.performance.logging.enabled", enablePerformanceLogging);
        
        // 内存管理配置
        maxCachedMethods = getIntProperty("tabby.analysis.max.cached.methods", maxCachedMethods);
        memoryCleanupThresholdMb = getLongProperty("tabby.analysis.memory.cleanup.threshold.mb", memoryCleanupThresholdMb);
        
        // 线程池配置
        corePoolSize = getIntProperty("tabby.analysis.thread.pool.core.size", corePoolSize);
        maximumPoolSize = getIntProperty("tabby.analysis.thread.pool.max.size", maximumPoolSize);
        keepAliveTimeMs = getLongProperty("tabby.analysis.thread.pool.keep.alive.ms", keepAliveTimeMs);
        
        logConfiguration();
    }
    
    /**
     * 获取整型系统属性
     */
    private int getIntProperty(String key, int defaultValue) {
        try {
            String value = System.getProperty(key);
            return value != null ? Integer.parseInt(value) : defaultValue;
        } catch (NumberFormatException e) {
            log.warn("Invalid integer value for property {}, using default: {}", key, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 获取长整型系统属性
     */
    private long getLongProperty(String key, long defaultValue) {
        try {
            String value = System.getProperty(key);
            return value != null ? Long.parseLong(value) : defaultValue;
        } catch (NumberFormatException e) {
            log.warn("Invalid long value for property {}, using default: {}", key, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 获取布尔型系统属性
     */
    private boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = System.getProperty(key);
        return value != null ? Boolean.parseBoolean(value) : defaultValue;
    }

    /**
     * 获取双精度浮点型系统属性
     */
    private double getDoubleProperty(String key, double defaultValue) {
        try {
            String value = System.getProperty(key);
            return value != null ? Double.parseDouble(value) : defaultValue;
        } catch (NumberFormatException e) {
            log.warn("Invalid double value for property {}, using default: {}", key, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 记录配置信息
     */
    private void logConfiguration() {
        if (log.isInfoEnabled()) {
            log.info("Analysis Configuration:");
            log.info("  Wait timeout: {}ms", waitTimeoutMs);
            log.info("  Task cleanup interval: {}ms", taskCleanupIntervalMs);
            log.info("  Max task age: {}ms", maxTaskAgeMs);
            log.info("  Max recursion depth: {}", maxRecursionDepth);
            log.info("  Initial bitset size: {}", initialBitSetSize);
            log.info("  Statistics enabled: {}", enableStatistics);
            log.info("  Performance logging enabled: {}", enablePerformanceLogging);
            log.info("  Max cached methods: {}", maxCachedMethods);
            log.info("  Memory cleanup threshold: {}MB", memoryCleanupThresholdMb);
            log.info("  Thread pool core size: {}", corePoolSize);
            log.info("  Thread pool max size: {}", maximumPoolSize);
            log.info("  Thread keep alive time: {}ms", keepAliveTimeMs);
        }
    }
    
    /**
     * 验证配置的有效性
     */
    public void validate() {
        if (waitTimeoutMs <= 0) {
            throw new IllegalArgumentException("Wait timeout must be positive");
        }
        if (taskCleanupIntervalMs <= 0) {
            throw new IllegalArgumentException("Task cleanup interval must be positive");
        }
        if (maxTaskAgeMs <= 0) {
            throw new IllegalArgumentException("Max task age must be positive");
        }
        if (maxRecursionDepth <= 0) {
            throw new IllegalArgumentException("Max recursion depth must be positive");
        }
        if (initialBitSetSize <= 0) {
            throw new IllegalArgumentException("Initial bitset size must be positive");
        }
        if (maxCachedMethods <= 0) {
            throw new IllegalArgumentException("Max cached methods must be positive");
        }
        if (memoryCleanupThresholdMb <= 0) {
            throw new IllegalArgumentException("Memory cleanup threshold must be positive");
        }
        if (corePoolSize <= 0) {
            throw new IllegalArgumentException("Core pool size must be positive");
        }
        if (maximumPoolSize < corePoolSize) {
            throw new IllegalArgumentException("Maximum pool size must be >= core pool size");
        }
        if (keepAliveTimeMs < 0) {
            throw new IllegalArgumentException("Keep alive time must be non-negative");
        }
    }
    
    /**
     * 获取等待超时时间（TimeUnit格式）
     */
    public long getWaitTimeout(TimeUnit unit) {
        return unit.convert(waitTimeoutMs, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 获取任务清理间隔（TimeUnit格式）
     */
    public long getTaskCleanupInterval(TimeUnit unit) {
        return unit.convert(taskCleanupIntervalMs, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 获取最大任务存活时间（TimeUnit格式）
     */
    public long getMaxTaskAge(TimeUnit unit) {
        return unit.convert(maxTaskAgeMs, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 获取统计报告间隔（TimeUnit格式）
     */
    public long getStatisticsReportInterval(TimeUnit unit) {
        return unit.convert(statisticsReportIntervalMs, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 获取线程保活时间（TimeUnit格式）
     */
    public long getKeepAliveTime(TimeUnit unit) {
        return unit.convert(keepAliveTimeMs, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 重新加载配置
     */
    public void reload() {
        loadFromSystemProperties();
        validate();
        log.info("Analysis configuration reloaded");
    }
    
    /**
     * 重置为默认配置
     */
    public void resetToDefaults() {
        waitTimeoutMs = 30000;
        taskCleanupIntervalMs = 60000;
        maxTaskAgeMs = 300000;
        maxRecursionDepth = 50;
        initialBitSetSize = 1024;
        enableStatistics = true;
        statisticsReportIntervalMs = 60000;
        enablePerformanceLogging = false;
        maxCachedMethods = 10000;
        memoryCleanupThresholdMb = 100;
        corePoolSize = Runtime.getRuntime().availableProcessors();
        maximumPoolSize = Runtime.getRuntime().availableProcessors() * 2;
        keepAliveTimeMs = 60000;
        
        log.info("Analysis configuration reset to defaults");
    }
    
    /**
     * 获取配置摘要
     */
    public String getConfigurationSummary() {
        return String.format(
            "AnalysisConfiguration{waitTimeout=%dms, maxDepth=%d, statistics=%s, maxCached=%d}",
            waitTimeoutMs, maxRecursionDepth, enableStatistics, maxCachedMethods
        );
    }
}
