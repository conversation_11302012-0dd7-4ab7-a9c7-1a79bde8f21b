package tabby.analysis.common;

import lombok.extern.slf4j.Slf4j;

import java.util.BitSet;
import java.util.concurrent.ConcurrentHashMap;
import java.lang.ThreadLocal;

/**
 * 高效的递归检测器
 * 使用位图和线程本地存储来优化递归检测性能
 * 
 * <AUTHOR>
 * @since 2024/01/01
 */
@Slf4j
public class RecursionDetector {
    
    // 线程本地的调用栈状态 - 直接使用字符串而不是ID映射
    private static final ThreadLocal<CallStackState> threadLocalState =
        ThreadLocal.withInitial(CallStackState::new);
    
    /**
     * 调用栈状态 - 使用内存高效的方案
     */
    private static class CallStackState {
        // 使用轻量级的调用栈，只存储当前调用链
        private final java.util.ArrayDeque<String> callStack = new java.util.ArrayDeque<>();
        // 为了快速查找，使用小容量的HashSet（只存储当前调用链）
        private final java.util.Set<String> callStackSet = new java.util.HashSet<>();

        // 获取最大深度限制
        private int getMaxDepth() {
            return AnalysisConfiguration.getInstance().getMaxRecursionDepth();
        }

        /**
         * 进入方法调用
         */
        public boolean enterMethod(String methodSignature) {
            if (callStack.size() >= getMaxDepth()) {
                return false; // 超过最大深度
            }

            if (callStackSet.contains(methodSignature)) {
                return false; // 检测到递归
            }

            callStack.push(methodSignature);
            callStackSet.add(methodSignature);
            return true;
        }

        /**
         * 退出方法调用
         */
        public void exitMethod(String methodSignature) {
            // 从栈顶移除（应该是当前方法）
            if (!callStack.isEmpty() && callStack.peek().equals(methodSignature)) {
                callStack.pop();
                callStackSet.remove(methodSignature);
            } else {
                // 异常情况：方法签名不匹配，清理整个栈
                callStack.clear();
                callStackSet.clear();
            }
        }

        /**
         * 检查是否在递归中
         */
        public boolean isInRecursion(String methodSignature) {
            return callStackSet.contains(methodSignature);
        }

        /**
         * 获取当前深度
         */
        public int getDepth() {
            return callStack.size();
        }

        /**
         * 清理状态
         */
        public void clear() {
            callStack.clear();
            callStackSet.clear();
        }
    }
    
    /**
     * 进入方法分析
     *
     * @param methodSignature 方法签名
     * @return RecursionCheckResult 检查结果
     */
    public static RecursionCheckResult enterMethod(String methodSignature) {
        CallStackState state = threadLocalState.get();

        // 检查是否已经在递归中
        if (state.isInRecursion(methodSignature)) {
            return new RecursionCheckResult(false, "Recursive call detected: " + methodSignature);
        }

        // 检查深度限制
        int maxDepth = AnalysisConfiguration.getInstance().getMaxRecursionDepth();
        if (state.getDepth() >= maxDepth) {
            return new RecursionCheckResult(false, "Max depth exceeded: " + state.getDepth());
        }

        // 尝试进入方法
        if (state.enterMethod(methodSignature)) {
            return new RecursionCheckResult(true, null);
        } else {
            return new RecursionCheckResult(false, "Failed to enter method: " + methodSignature);
        }
    }

    /**
     * 退出方法分析
     *
     * @param methodSignature 方法签名
     */
    public static void exitMethod(String methodSignature) {
        CallStackState state = threadLocalState.get();
        state.exitMethod(methodSignature);
    }

    /**
     * 检查是否在递归中
     *
     * @param methodSignature 方法签名
     * @return 是否在递归中
     */
    public static boolean isInRecursion(String methodSignature) {
        CallStackState state = threadLocalState.get();
        return state.isInRecursion(methodSignature);
    }
    
    /**
     * 获取当前调用深度
     * 
     * @return 当前深度
     */
    public static int getCurrentDepth() {
        CallStackState state = threadLocalState.get();
        return state.getDepth();
    }
    
    /**
     * 清理当前线程的状态
     */
    public static void clearCurrentThread() {
        CallStackState state = threadLocalState.get();
        state.clear();
    }
    
    /**
     * 清理所有状态（用于系统关闭）
     */
    public static void clearAll() {
        threadLocalState.remove();
    }

    /**
     * 获取当前线程的调用栈大小（用于监控）
     */
    public static int getCurrentCallStackSize() {
        CallStackState state = threadLocalState.get();
        return state.getDepth();
    }
    
    /**
     * 递归检查结果
     */
    public static class RecursionCheckResult {
        private final boolean canProceed;
        private final String reason;
        
        public RecursionCheckResult(boolean canProceed, String reason) {
            this.canProceed = canProceed;
            this.reason = reason;
        }
        
        public boolean canProceed() {
            return canProceed;
        }
        
        public String getReason() {
            return reason;
        }
        
        public boolean isRecursive() {
            return !canProceed && reason != null && reason.contains("Recursive call");
        }
        
        public boolean isDepthExceeded() {
            return !canProceed && reason != null && reason.contains("Max depth exceeded");
        }
    }
    
    /**
     * 自动管理的方法分析上下文
     * 使用try-with-resources语法自动管理进入和退出
     */
    public static class MethodAnalysisContext implements AutoCloseable {
        private final String methodSignature;
        private final boolean entered;
        
        private MethodAnalysisContext(String methodSignature, boolean entered) {
            this.methodSignature = methodSignature;
            this.entered = entered;
        }
        
        /**
         * 创建方法分析上下文
         * 
         * @param methodSignature 方法签名
         * @return 分析上下文，如果无法进入则返回null
         */
        public static MethodAnalysisContext create(String methodSignature) {
            RecursionCheckResult result = enterMethod(methodSignature);
            if (result.canProceed()) {
                return new MethodAnalysisContext(methodSignature, true);
            } else {
                log.debug("Cannot enter method analysis: {}, reason: {}", methodSignature, result.getReason());
                return new MethodAnalysisContext(methodSignature, false);
            }
        }
        
        public boolean isEntered() {
            return entered;
        }
        
        @Override
        public void close() {
            if (entered) {
                exitMethod(methodSignature);
            }
        }
    }
}
