package tabby.analysis.v2.model;

import soot.Type;
import soot.jimple.InvokeExpr;
import tabby.common.bean.ref.MethodReference;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2021/9/15
 */
public class ThreadRunnableInvokeModel extends Model {

    private static String RUNNABLE_RUN = "void run()";

    @Override
    boolean apply(InvokeExpr invokeExpr, MethodReference callee) {
        if ("java.lang.Thread".equals(callee.getClassname())
                && "<init>".equals(callee.getName()) && callee.isHasParameters()) {

            // 找到runnable参数位置
            int index = -1;
            Type sootType = null;
            for (int i = 0; i < callee.getParameterSize(); i++) {
                sootType = getArgType(invokeExpr, i);
                if (sootType != null && isAssignableFrom(sootType.toString(), Runnable.class)) {
                    index = i;
                    break;
                }
            }

            if(sootType == null || index == -1) return false;
            Set<String> argTypes = cgBuilder.types.get(index+1);

            Set<MethodReference> refs = getReferences(argTypes, sootType, RUNNABLE_RUN);

            if(refs.isEmpty()) return false;

            Set<String> objTypes = new HashSet<>(argTypes);
            cgBuilder.getTypes().clear();
            cgBuilder.getTypes().add(objTypes);
            cgBuilder.setPositions(mergePositions(cgBuilder.getPositions()));
            cgBuilder.setInvokeType("ManualInvoke");
            cgBuilder.setCallerThisFieldObj(false);

            needToReplace.addAll(refs);
            return true;
        }
        return false;
    }
}
