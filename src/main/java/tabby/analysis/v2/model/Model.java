package tabby.analysis.v2.model;

import com.google.common.collect.ImmutableMap;
import soot.SootClass;
import soot.Type;
import soot.Value;
import soot.jimple.InvokeExpr;
import tabby.analysis.v2.container.ValueContainer;
import tabby.common.bean.ref.MethodReference;
import tabby.common.utils.PositionUtils;
import tabby.common.utils.SemanticUtils;
import tabby.core.container.DataContainer;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2021/8/31
 */
public abstract class Model {

    public static Map<String, String> SUB_SIGNATURES
            = ImmutableMap.of(
            "RUNNABLE_RUN", "void run()", // runnable.run
            "P_ACTION", "java.lang.Object run()", // PrivilegedAction.run
            "CALLABLE_RUN", "void call()",
            "PROXY_INVOKE", "java.lang.Object invoke(java.lang.Object,java.lang.reflect.Method,java.lang.Object[])",
            "HIBERNATE3_EXECUTE", "java.lang.Object doInHibernate(org.hibernate.Session)",
            "OPTIONAL_CONSUMER", "void accept(java.lang.Object)",
            "OPTIONAL_FUNCTION", "java.lang.Object apply(java.lang.Object)"
    );

    public ValueContainer container = null;
    public DataContainer dataContainer = null;
    public CallEdgeBuilder cgBuilder = null;
    public Set<MethodReference> needToIgnore = new HashSet<>();
    public Set<MethodReference> needToReplace = new HashSet<>(); // 用于记录新的需要构建函数边的函数

    public void accept(CallEdgeBuilder cgBuilder){
        this.cgBuilder = cgBuilder;
        container = cgBuilder.getContext().getContainer();
        dataContainer = cgBuilder.getContext().getDataContainer();
        needToIgnore.clear();
        needToReplace.clear();
    }

    public boolean apply(InvokeExpr ie) {
        for(MethodReference callee:cgBuilder.getNeedToBuildCallEdge()){
            if(apply(ie, callee)){ // 返回true，则忽略当前函数的建边，一般会有新的函数作为替换
                needToIgnore.add(callee);
            }
        }

        // TODO needToAnalyze 数组理论上也可以被替换掉，更精确处理
        cgBuilder.getCandidateToBuildCallEdge().addAll(needToReplace);
        cgBuilder.getNeedToBuildCallEdge().removeAll(needToIgnore);
        if(cgBuilder.getNeedToBuildCallEdge().isEmpty()){
            return true;
        }

        if(this instanceof DefaultInvokeModel && !cgBuilder.getNeedToBuildCallEdge().isEmpty()){ // last model
            cgBuilder.getCandidateToBuildCallEdge().addAll(cgBuilder.getNeedToBuildCallEdge());
        }

        return false;
    }

    abstract boolean apply(InvokeExpr invokeExpr, MethodReference callee);

    public String getSubSignature(String classname, String func, String bytecodeParams, String defaultSignature) {
        // 不是很有必要
//        try{
//            SootClass cls = Scene.v().getSootClass(classname);
//            if(cls.getMethodCount() > 0){
//                for(SootMethod method:cls.getMethods()){
//                    if(func.equals(method.getName())
//                            && bytecodeParams.equals(method.getBytecodeParms())){ // 找到的第一个符合要求的名字
//                        return method.getSubSignature();
//                    }
//                }
//            }
//        }catch (Exception e){
//            // class not found
//        }
        return SUB_SIGNATURES.get(defaultSignature);
    }


    public Type getArgType(InvokeExpr ie, int index) {
        try {
            Value arg = ie.getArg(index);
            if (arg != null) {
                return arg.getType();
            }
        } catch (Exception e) {
            // class not found
        }
        return null;
    }

    public boolean isAssignableFrom(String target, Class<?> cls) {

        if (target.equals(cls.getName())) return true;

        try {
            SootClass targetClass = SemanticUtils.getSootClass(target);
            Set<String> allFatherNodes = SemanticUtils.getAllFatherNodes(targetClass, true);
            return allFatherNodes.contains(cls.getName());
        } catch (Exception ignored) {
        }

        return false;
    }

    public List<Set<Integer>> mergePositions(List<Set<Integer>> origin){
        Set<Integer> set = new HashSet<>();
        for (Set<Integer> pos : origin) {
            set.addAll(pos);
        }
        set.remove(PositionUtils.NOT_POLLUTED_POSITION);
        if (set.isEmpty()) {
            set.add(PositionUtils.NOT_POLLUTED_POSITION);
        }
        List<Set<Integer>> positions = new LinkedList<>();
        positions.add(set);
        return positions;
    }

    public MethodReference getMethodReference(String classname, String subSignature){
        return dataContainer.getOrAddMethodRefBySubSignature(classname, subSignature);
    }

    public Set<MethodReference> getReferences(Set<String> types, Type sootType, String subSignature){
        Set<MethodReference> refs = new HashSet<>();
        // 优先从types取
        for(String type : types){
            MethodReference methodRef = getMethodReference(type, subSignature);
            if(methodRef != null){
                refs.add(methodRef);
            }
        }
        // 如果types里没有合适的，那么从sootType取
        if(refs.isEmpty() && sootType != null){
            MethodReference methodRef = getMethodReference(sootType.toString(), subSignature);
            if(methodRef != null){
                refs.add(methodRef);
                types.clear();
                types.add(sootType.toString()); // 更新types
            }
        }
        return refs;
    }
}
