package tabby.analysis.v2.model;

import soot.jimple.InvokeExpr;
import tabby.common.bean.ref.MethodReference;
import tabby.common.utils.PositionUtils;
import tabby.config.GlobalConfiguration;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2021/9/1
 */
public class IgnoreInvokeModel extends Model {

    private static final List<String> IGNORE_LIST = new ArrayList<>(Arrays.asList(
            "<java.lang.Object: void <init>()>",
            "<java.io.PrintStream: void println(java.lang.String)>",
            "<java.lang.StringBuilder: java.lang.StringBuilder append(java.lang.String)>",
            "<java.lang.StringBuilder: java.lang.AbstractStringBuilder append(float)>",
            "<java.lang.StringBuilder: java.lang.String toString()>",
            "<java.lang.StringBuilder: int length()>",
            "<java.lang.String: int hashCode()>",
            "<java.lang.String: int length()>",
            "<java.lang.String: boolean equals(java.lang.Object)>"
    ));

    private static final List<String> WEB_MODE_IGNORE_LIST = new ArrayList<>(Arrays.asList(
            "java.lang.String toString()",
            "int hashCode()",
            "void close()",
            "void flush()",
            "int length()",
            "java.lang.Class getClass()",
            "boolean equals(java.lang.Object)"
    ));

    private static final List<String> EXCLUDE_LIST = new ArrayList<>(Arrays.asList(
            "contains",
            "equals"
    ));

    private static final List<String> WEB_MODE_IGNORE_CLASSNAME_LIST = new ArrayList<>(Arrays.asList(
            "java.util.List",
            "java.util.ArrayList",
            "java.util.CopyOnWriteArrayList",
            "java.util.LinkedList",
            "java.util.Stack",
            "java.util.Vector",
            "java.util.Set",
            "java.util.LinkedHashSet",
            "java.util.TreeSet",
            "java.util.HashSet",
            "java.util.Collection",
            "java.util.Queue",
            "java.util.AbstractQueue",
            "java.util.PriorityQueue",
            "java.util.ArrayDeque",
            "java.util.HashTable",
            "java.util.concurrent.CopyOnWriteArraySet",
            "java.util.Map",
            "java.util.HashMap",
            "java.util.TreeMap",
            "java.util.LinkedHashMap",
            "java.util.WeakHashMap",
            "java.util.SortedMap",
            "java.util.concurrent.ConcurrentHashMap",
            "java.util.concurrent.ConcurrentMap",
            "java.util.Arrays",
            "java.util.Properties",
            "java.util.Iterator",
            "java.lang.Iterable",
            "java.util.Enumeration",
            "java.io.PrintStream",
            "java.lang.String",
            "java.lang.StringBuilder",
            "java.util.Optional"
    ));

    @Override
    public boolean apply(InvokeExpr ie) {
        // 主要检查是否要剔除无用的函数调用
        if(GlobalConfiguration.IS_NEED_REMOVE_NOT_POLLUTED_CALL_SITE){
            boolean isNotContainsTaintedVar = false;
            Set<Integer> copied = new HashSet<>();

            for(Set<Integer> position : cgBuilder.getPositions()){
                copied.addAll(position);
            }

            if(copied.size() == 1 && copied.contains(PositionUtils.NOT_POLLUTED_POSITION)){
                isNotContainsTaintedVar = true;
            }

            if(isNotContainsTaintedVar){
                cgBuilder.getNeedToBuildCallEdge().clear(); // 如果不包含污点，则直接清除所有
            }
        }

        if(!cgBuilder.getNeedToBuildCallEdge().isEmpty()){
            for(MethodReference callee : cgBuilder.getNeedToBuildCallEdge()){
                if(apply(ie, callee)){
                    needToIgnore.add(callee);
                }
            }
        }

        if(!needToIgnore.isEmpty()){
            cgBuilder.getNeedToBuildCallEdge().removeAll(needToIgnore);
        }

        if(cgBuilder.getNeedToBuildCallEdge().isEmpty()){
            return true;
        }else{
            // 还有非ignore的，继续传播
            return false;
        }
    }

    @Override
    boolean apply(InvokeExpr ie, MethodReference callee) {
        if(GlobalConfiguration.DEBUG) return false; // debug 模式下不删除调用边

        String signature = callee.getSignature();
        if (IGNORE_LIST.contains(signature)) {
            return true;
        }

        // 如果是web模式，减少记录一些无关的函数调用，尽可能提高关键路径占比
        if (GlobalConfiguration.IS_WEB_MODE) {
            String name = callee.getName();
            if (EXCLUDE_LIST.contains(name)) {
                return true;
            }

            String subSignature = callee.getSubSignature();
            if (WEB_MODE_IGNORE_LIST.contains(subSignature)) {
                return true;
            }

            String classname = callee.getClassname();
            if (WEB_MODE_IGNORE_CLASSNAME_LIST.contains(classname)) {
                return true;
            }
        }

        return false;
    }
}
