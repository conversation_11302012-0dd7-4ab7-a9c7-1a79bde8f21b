package tabby.analysis.v2.model;

import soot.Type;
import soot.jimple.InvokeExpr;
import tabby.common.bean.ref.MethodReference;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2021/8/31
 */
public class AccessControllerInvokeModel extends Model {

    private static String P_ACTION = "java.lang.Object run()";

    @Override
    boolean apply(InvokeExpr ie, MethodReference callee) {
        if ("java.security.AccessController".equals(callee.getClassname())
                && "doPrivileged".equals(callee.getName())) {

            Type type = getArgType(ie, 0);
            Set<String> argTypes = cgBuilder.types.get(1); // 第一个参数
            Set<MethodReference> refs = getReferences(argTypes, type, P_ACTION);

            if(refs.isEmpty()) return false;

            Set<String> copied = new HashSet<>(argTypes);
            cgBuilder.getTypes().clear();
            cgBuilder.getTypes().add(copied);
            cgBuilder.setPositions(mergePositions(cgBuilder.getPositions()));
            cgBuilder.setInvokeType("ManualInvoke");
            cgBuilder.setCallerThisFieldObj(false);

            needToReplace.addAll(refs);
            return true;
        }

        return false;
    }

}
