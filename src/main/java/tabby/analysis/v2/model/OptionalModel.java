package tabby.analysis.v2.model;


import soot.Type;
import soot.jimple.InvokeExpr;
import tabby.common.bean.ref.MethodReference;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 处理java.util.Optional调用 ifPresent、ifPresentOrElse、map、flatMap
 * @project tabby_ng
 * @since 2025/7/25
 */
public class OptionalModel extends Model {

    private static String OPTIONAL_CONSUMER = "void accept(java.lang.Object)";
    private static String OPTIONAL_FUNCTION = "java.lang.Object apply(java.lang.Object)";


    @Override
    boolean apply(InvokeExpr invokeExpr, MethodReference callee) {

        if("java.util.Optional".equals(callee.getClassname()) && ("ifPresent".equals(callee.getName()) || "ifPresentOrElse".equals(callee.getName()))){
            Type type = getArgType(invokeExpr, 0);
            Set<String> consumerTypes = cgBuilder.types.get(1); // 获取参数的类型
            Set<MethodReference> refs = getReferences(consumerTypes, type, OPTIONAL_CONSUMER);
            if(refs.isEmpty()) return false;

            Set<String> copied = new HashSet<>(consumerTypes);
            cgBuilder.getTypes().clear();
            cgBuilder.getTypes().add(copied);

            List<Set<Integer>> pos = new ArrayList<>();
            pos.add(cgBuilder.getPositions().get(1));
            pos.add(cgBuilder.getPositions().get(0));
            cgBuilder.setPositions(pos);

            cgBuilder.setInvokeType("ManualInvoke");
            cgBuilder.setCallerThisFieldObj(false);

            needToReplace.addAll(refs);
            return true;
        }else if("java.util.Optional".equals(callee.getClassname()) && ("map".equals(callee.getName()) || "flatMap".equals(callee.getName()))){
            Type type = getArgType(invokeExpr, 0);
            Set<String> functionTypes = cgBuilder.types.get(1); // 获取参数的类型
            Set<MethodReference> refs = getReferences(functionTypes, type, OPTIONAL_FUNCTION);
            if(refs.isEmpty()) return false;

            Set<String> copied = new HashSet<>(functionTypes);
            cgBuilder.getTypes().clear();
            cgBuilder.getTypes().add(copied);

            List<Set<Integer>> pos = new ArrayList<>();
            pos.add(cgBuilder.getPositions().get(1));
            pos.add(cgBuilder.getPositions().get(0));
            cgBuilder.setPositions(pos);

            cgBuilder.setInvokeType("ManualInvoke");
            cgBuilder.setCallerThisFieldObj(false);

            needToReplace.addAll(refs);
            return true;
        }


        return false;
    }


}
