package tabby.analysis.v2.model;

import soot.Type;
import soot.jimple.InvokeExpr;
import tabby.common.bean.ref.MethodReference;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @since 2021/9/15
 */
public class ThreadPoolRunnableInvokeModel extends Model {

    private static String RUNNABLE_RUN = "void run()";
    private static String CALLABLE_RUN = "void call()";

    @Override
    boolean apply(InvokeExpr ie, MethodReference callee) {
        String targetCls = callee.getClassname();
        if (targetCls == null) {
            return false;
        }
        boolean isExecute = isAssignableFrom(targetCls, Executor.class) && "execute".equals(callee.getName());
        boolean isSubmit = isAssignableFrom(targetCls, ExecutorService.class) && "submit".equals(callee.getName());
        if(!isExecute && !isSubmit) return false;

        Set<String> argTypes = cgBuilder.types.get(1);
        Type sootType = getArgType(ie, 0);
        String subSignature = getSubSignature(targetCls, sootType, isExecute, isSubmit);
        Set<MethodReference> refs = getReferences(argTypes, sootType, subSignature);

        if(refs.isEmpty()){
            return false;
        }

        Set<String> copied = new HashSet<>(argTypes);
        cgBuilder.getTypes().clear();
        cgBuilder.getTypes().add(copied);
        cgBuilder.setPositions(mergePositions(cgBuilder.getPositions()));
        cgBuilder.setInvokeType("ManualInvoke");
        cgBuilder.setCallerThisFieldObj(false);

        needToReplace.addAll(refs);
        return true;
    }

    public String getSubSignature(String classname, Type sootType, boolean isExecute, boolean isSubmit) {
        if(isExecute){
            return RUNNABLE_RUN;
        }else if(isSubmit){
            if(isAssignableFrom(classname, Runnable.class) || isAssignableFrom(sootType.toString(), Runnable.class)){ // java.lang.Runnable#run
                return RUNNABLE_RUN;
            }else if(isAssignableFrom(classname, Callable.class) || isAssignableFrom(sootType.toString(), Callable.class)){ // java.util.concurrent.Callable#call
                return CALLABLE_RUN;
            }
        }
        return null;
    }
}
