package tabby.analysis.v2.switcher;


import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;
import soot.*;
import soot.jimple.*;
import tabby.analysis.v2.CachedPointerAnalysis;
import tabby.analysis.v2.container.ValueContainer;
import tabby.analysis.v2.data.Context;
import tabby.analysis.v2.data.Taint;
import tabby.analysis.v2.data.actions.MultiAction;
import tabby.analysis.v2.data.nodes.*;
import tabby.analysis.v2.model.CallEdgeBuilder;
import tabby.common.bean.edge.Call;
import tabby.common.bean.ref.MethodReference;
import tabby.common.utils.PositionUtils;
import tabby.common.utils.SemanticUtils;
import tabby.config.GlobalConfiguration;
import tabby.core.collector.ClassInfoCollector;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @project tabby_ng
 * @since 2025/4/2
 */
public class ExprSwitcher extends NodeSwitcher {

    public Set<Call> callEdges = null;
    public Stmt stmt = null;
    public CallEdgeBuilder cgBuilder = new CallEdgeBuilder();

    public ExprSwitcher() {
    }

    public void accept(Set<Call> callEdges) {
        this.callEdges = callEdges;
    }

    public void accept(Stmt stmt) {
        this.stmt = stmt;
    }

    /**
     * a = b;
     * 其他类型 field类型、array类型 都通过NodeSwitcher的实现来做
     * @param v
     */
    @Override
    public void caseLocal(Local v) {
        // 右侧 Local 不可能凭空出现，所以没找到就是空的，不新增
        setResult(container.getNodes(v));
    }

    /**
     * 右侧 a[],a[][]，代表 array load
     * 如果是空的，需要新建一个array的node
     * @param v
     */
    @Override
    public void caseArrayRef(ArrayRef v) {
        Set<Node> ret = new HashSet<>();
        Set<Node> nodes = container.getNodes(v);
        if(nodes.isEmpty()) return;
        nodes.forEach(node -> {
            if(node instanceof ArrayNode arrayNode) {
                arrayNode.getArray().forEach(nodeRef -> {
                    Node a = container.getNode(nodeRef);
                    if(a != null){
                        ret.add(a);
                    }
                });
            }
        });

        if(ret.isEmpty()){
            // 根据baseType新建一个array node
            for(Node node:nodes){
                if(node instanceof ArrayNode an){
                    String baseType = v.getType().toString();
                    SootClass cls = SemanticUtils.getSootClass(baseType);
                    if(cls != null){
                        String identity = SemanticUtils.judgeNodeType(cls);
                        Node arrayNode = Node.ofEmptyNode(identity);
                        arrayNode.setUid(identity + SemanticUtils.calculateUuid(v).substring(0, 8));
                        an.addArrayNode(arrayNode, container, new HashSet<>(), true);
                        container.save(arrayNode);
                        container.save(an);
                        ret.add(arrayNode);
                    }
                }
            }
        }

        setResult(ret);
    }

    /**
     * a = (A) value;
     * 只返回当前value，所以只做转发
     *
     * @param v
     */
    @Override
    public void caseCastExpr(CastExpr v) {
        Value value = v.getOp();
        value.apply(this);
        Object node = getResult();
        // 更新类型
        if(node instanceof Set){
            Set<Node> set = (Set<Node>) node;
            Set<Node> ret = new HashSet<>();
            for(Node n : set){
                if(n instanceof ConstantNode){
                    Node copied = n.clone();
                    copied.resetUid(v.toString(), false);
                    copied.setType(v.getCastType());
                    ret.add(copied);
                    container.saveIfAbsent(copied);
                }else {
                    n.setType(v.getCastType());
                    ret.add(n);
                    container.saveIfAbsent(n);
                }
            }
            setResult(ret);
        }
    }

    /**
     * a = new xxx
     *
     * @param v
     */
    @Override
    public void caseNewExpr(NewExpr v) {
        Set<Node> nodes = new HashSet<>();
        Type type = v.getType();
        nodes.add(NewNode.of(type));
        setResult(nodes);

        // 确保类空间和函数空间有当前type，没有则重新载入
        String classname = type.toQuotedString();
        ClassInfoCollector.recollect(classname, dataContainer);
    }

    /**
     * a = newarray xxx
     * a = Obj[] 这种结构
     * @param v
     */
    @Override
    public void caseNewArrayExpr(NewArrayExpr v) {
        // TODO 这个位置可以扩展来检查反序列化OOM的利用点，新版暂不实现
        Set<Node> nodes = new HashSet<>();
        Type type = v.getType();
        nodes.add(NewNode.of(type));
        setResult(nodes);
    }

    /**
     * a = newmultiarray xxx
     * a = Obj[][] 这种结构，多维数组，这里简化成一维数组
     * @param v
     */
    @Override
    public void caseNewMultiArrayExpr(NewMultiArrayExpr v) {
        // TODO 这个位置可以扩展来检查反序列化OOM的利用点，新版暂不实现
        Set<Node> nodes = new HashSet<>();
        Type type = v.getType();
        nodes.add(NewNode.of(type));
        setResult(nodes);
    }

    /**
     * a = b+c;
     * 返回一个set
     * @param v
     */
    @Override
    public void caseAddExpr(AddExpr v) {
        caseMathExpr(v);
    }

    @Override
    public void caseDivExpr(DivExpr v) {
        caseMathExpr(v);
    }

    @Override
    public void caseMulExpr(MulExpr v) {
        caseMathExpr(v);
    }

    /**
     * a = b-c;
     * 返回一个set
     * @param v
     */
    @Override
    public void caseSubExpr(SubExpr v) {
        caseMathExpr(v);
    }

    public void caseMathExpr(BinopExpr v) { // 进当前函数的都是constant
        Set<Node> ret = new HashSet<>();
        Set<Taint> taints = new HashSet<>();
        for (ValueBox box : v.getUseBoxes()) {
            Value value = box.getValue();
            Set<Node> nodes = container.getNodes(value);
            if(nodes.isEmpty()) continue;
            for(Node n : nodes){
                if(Taint.isContainsTaint(n.getTaints(), null)){
                    taints.addAll(n.getTaints());
                }
            }
        }
        IgnoreNode ignoreNode = new IgnoreNode();
        if(!taints.isEmpty()){
            ignoreNode.setTaints(taints);
            ignoreNode.setNeedTransfer(true);
        }
        ret.add(ignoreNode);
        setResult(ret);
    }

    /**
     * 处理Invoke类型的expr
     * caseInterfaceInvokeExpr
     * caseSpecialInvokeExpr
     * caseStaticInvokeExpr
     * caseVirtualInvokeExpr
     * caseDynamicInvokeExpr
     *
     * @param v
     */
    public void caseInvokeExpr(InvokeExpr v) {
        // pre process
        if (v instanceof StaticInvokeExpr && v.getArgCount() == 0 && !GlobalConfiguration.IS_NEED_ANALYSIS_EVERYTHING){
            return; // 静态调用且不传参数，不影响后续分析
        }else if(v instanceof SpecialInvokeExpr && v.getMethodRef().getSubSignature().toString().equals("void <init>()")){
            return; // 没有参数的构造函数，可以忽略
        }

        // make call edges
        cgBuilder.accept(context);
        cgBuilder.accept(stmt);
        cgBuilder.build(v, callEdges);

        // 当不存在污染变量时，返回值不影响全局污点传播，所以不分析此种情况的调用
        if (isNotContainsTaintedInfo(cgBuilder.getPositions())
                && v.getArgCount() == 0 &&
                !GlobalConfiguration.IS_NEED_ANALYSIS_EVERYTHING) {
            setResult(Sets.newHashSet(IgnoreNode.of()));
            return;
        }
        // 存在污染变量时，将递归处理下一个函数
        Set<MethodReference> targets = cgBuilder.getCandidateToAnalyze();
        if (targets.isEmpty()) {
            simplify(v);
            return;
        }
        // process every target
        Type returnType = null;
        boolean isVoidReturnType = false;
        Iterator<MethodReference> iterator = targets.iterator();
        String[] objs = extractVarNames(v);
        Set<ValueContainer> tempContainers = new HashSet<>();
        Set<Node> returnNodes = new HashSet<>(); // only from simplify
        while (iterator.hasNext()) {
            MethodReference target = iterator.next();
            if (target == null || "<java.lang.Object: void <init>()>".equals(target.getSignature())) continue;
            String type = target.getReturnType().stream().findFirst().orElse(null);
            if (target.isInitialed() || target.isActionInitialed()) {
                tempContainers.add(
                        applySummary(target, target.getRuleActions(), target.getSummaries(), objs, type)
                );
                continue;
            } else if (target.isDao() || target.isRpc() || target.isMRpc()) {
                SemanticUtils.applySpecialMethodSummary(target, container);
                tempContainers.add(
                        applySummary(target, target.getRuleActions(), null, objs, type)
                );
                continue;
            }

            SootMethod method = target.getMethod();
            if (method == null) continue;

            returnType = method.getReturnType();
            if (!isVoidReturnType && returnType instanceof VoidType) {
                isVoidReturnType = true;
            }

            boolean flag = false;
            if (method.isPhantom() || method.isAbstract() || method.isNative()
                    || context.isInRecursion(target.getSignature()) || context.isOverMaxDepth()
                    || target.isContainSomeError()
            ) {
                flag = true;
            } else if (target.isEverTimeout()) {
                // 放到timeout地方
                context.setAnalyseTimeout(true);
                flag = true;
            } else {
                try (Context subContext = context.createSubContext(target)) {
                    subContext.setPreObjects(objs);
                    subContext.setCurObjects(new String[objs.length]);
                    if (CachedPointerAnalysis.processMethod(target.getMethod(), subContext)
                            && !subContext.isAnalyseTimeout() && !subContext.isForceStop()) {
                        tempContainers.add(
                                applySummary(target,null, target.getSummaries(), objs, returnType.toString())
                        );
                    } else {
                        flag = true;
                    }
                    context.sub(subContext.cost());
                }
            }

            if (flag) {
                // 由于没办法获取method body，或递归的情况，这里采取两种策略
                // 1. 返回值void，则保持当前函数调用者和参数的污点性，不做处理。这里可能存在参数污点丢失的情况
                // 2. 返回值不为void，则采用模糊处理，返回值污点信息copy函数调用舍和参数的所有污点信息，并将涉及的变量污点信息都统一化
                // 3. 分析出错的情况
                if (returnType instanceof VoidType || !(stmt instanceof AssignStmt)) continue;
                // 模糊处理
                simplify(v);
                Set<Node> ret = (Set<Node>) getResult();
                if(ret == null) continue;
                returnNodes.addAll(ret);
            }
        }

        tempContainers.remove(null);
        ValueContainer finalTempContainer = null;
        if(tempContainers.size() > 1){
            for(ValueContainer temp : tempContainers){
                if(finalTempContainer == null){
                    finalTempContainer = temp;
                }else{
                    finalTempContainer.union(temp);
                }
            }
        }else if(tempContainers.size() == 1){
            finalTempContainer = tempContainers.iterator().next();
        }
        // 使用finalTempContainer来替换相同的变量
        if(finalTempContainer != null){
            Set<Node> retNodes = finalTempContainer.removeNodesByVarName("return");
            container.replace(finalTempContainer);
            if(!isVoidReturnType && stmt instanceof AssignStmt){
                if(retNodes.isEmpty()){
                    setResult(Sets.newHashSet(IgnoreNode.of()));
                }else{
                    setResult(retNodes);
                }
            }
        }else{
            // 出现在actions和summaries都是空的情况，
            // 这种情况如果是assign语句，直接创建lop变量即可
            if(!isVoidReturnType && stmt instanceof AssignStmt){
                if(returnNodes.isEmpty()){
                    Set<Node> retNodes = Sets.newHashSet(IgnoreNode.of());
                    setResult(retNodes);
                }else{
                    setResult(returnNodes);
                }
            }
        }
    }

    /**
     * 通过函数摘要，将当前scope下的变量进行调整
     * 整体流程就是还原各类Node到当前context下
     * 把摘要中的this、param、ret 转化为调用边上的所涉及变量
     * 如果是非空返回，需要setResult对应的nodes列表
     *
     * callSiteVarNames 表示当前调用上所涉及到的变量，包括调用者，参数，涉及到的变量Node都存在当前container上面
     */
    public ValueContainer applySummary(
            MethodReference target,
            Multimap<String, String> actions,
            Multimap<String, Multimap<String, String>> summaries,
            String[] callSiteVarNames, String returnType) {
        container.setObjects(context.getCurObjects());
        if(actions != null && !actions.isEmpty()){
            // 处理规则的格式
            return applyRuleActions(actions, callSiteVarNames, returnType);
        }else if(summaries != null && !summaries.isEmpty()){
            // 处理序列化的格式
            // 还原得到 param-n this return 这3种var的container
            // 后续需要将这几个跟当前的container的node做比较，并将最新的变化复制到container下面
            // 还原得到的Node，如果跟现有container里的有冲突，则认为应该被替换成还原得到的Node
            // 由于uid在2个container不一定能对应上，那么冲突应该定义为
            if(summaries.size() > GlobalConfiguration.SUMMARIES_LIMIT){ // 检查summaries大小
                return applyRuleActions(target.getUnmodifiableActions(), callSiteVarNames, returnType); // 会忽略newed操作
            }else{
                return applySummaryActions(summaries, callSiteVarNames, returnType);
            }
        }
        return null;
    }

    /**
     * 把规则里的actions替换出具体的node
     * 规则只允许一层属性，比如 a<f>f1<f>f2 不应该出现在规则文件中
     * 1. 特殊标签 <s> 表示只需要把right的taints赋值给left即可, 标签应该只出现在末尾
     * 2. 特殊标签 <a> <k> <v> 分别表示取值或覆盖 array 或 map
     * 3. 特殊标签 <f> 表示field
     * 4. 2和3是互斥的，不应该同时出现，1可以和2或3搭配使用
     * @param actions
     * @param callSiteVarNames
     */
    public ValueContainer applyRuleActions(
            Multimap<String, String> actions, String[] callSiteVarNames, String returnType){
        Set<String> keys = new HashSet<>(actions.keySet());
        Set<String> returnSet = keys.stream().filter(key -> key.startsWith("return")).collect(Collectors.toSet());
        Set<String> normalAssignSet = Sets.difference(keys, returnSet).immutableCopy();
        ValueContainer tempContainer = container.createContainerWithInitialVars(callSiteVarNames);
        boolean isThisNodeChanged = false;
        for(String left : normalAssignSet){
            // get all rights' node refs
            isThisNodeChanged = left.startsWith("this");
            MultiAction leftAction = MultiAction.parse(left, callSiteVarNames, stmt, tempContainer, null, container);
            Set<Node> assignNodes = leftAction.getAssignNodes(actions.get(left), null, false);
            leftAction.assign(assignNodes, false, false);
        }

        if(!returnSet.isEmpty() && stmt instanceof AssignStmt as){
            // 从规则看，出现return一般不会涉及到return的f、k、v、a，只有s或没有s的情况
            // 并且return一般只有一个
            // 从编写规则的角度，也应该避免这种情况
            // create a return node
            Node returnNode = Node.ofReturnNode(as, returnType, new HashSet<>());
            Set<String> actionKeys = returnSet.stream().sorted(Comparator.comparingInt(String::length)).collect(Collectors.toCollection(LinkedHashSet::new));
            for(String left : actionKeys){
                Collection<String> rights = actions.get(left);
                if(rights.isEmpty()) continue;
                MultiAction leftAction = MultiAction.parse(left, callSiteVarNames, stmt, tempContainer, null, container);
                Set<Node> assignNodes = leftAction.getAssignNodes(rights, null,  isThisNodeChanged && rights.contains("this"));

                Set<Node> nodes = new HashSet<>();
                if(leftAction.getTailAction().isOnlyChangeTaints){
                    Set<Integer> positions = container.getTaintIdsByNodes(assignNodes);
                    returnNode.setTaints(
                            positions.stream().
                                    map(taintId -> Taint.of(taintId, ""))
                                    .collect(Collectors.toSet()));
                    nodes.add(returnNode);
                }else{
                    nodes.addAll(assignNodes);
                }
                leftAction.assign(nodes, false, true);
            }
        }
        return tempContainer;
    }

    /**
     * 通过summaries可以还原到当时分析时候的this、param-n、return
     * 还原后的内容所有污点其实指向的是当前调用边上的对象
     * 1. 根据还原后的node的taint获取到当前变量上的taint，
     *      如果当前变量有对应的node，可以直接替换当前container下的node；
     *      如果没有可以把还原的node taint修正后，assign到当前的内容下
     *
     *
     *
     * @param summaries
     * @param callSiteVarNames
     * @param returnType
     * @return
     */
    public ValueContainer applySummaryActions(Multimap<String, Multimap<String, String>> summaries,
                                              String[] callSiteVarNames, String returnType){
        // recover summaries container
        ValueContainer summaryContainer = new ValueContainer(summaries, container.getDataContainer());
        ValueContainer tempContainer = container.createContainerWithInitialVars(callSiteVarNames);
        // backtrack this/param-n
        for(int i=0;i<callSiteVarNames.length;i++){
            String key = i != 0 ? "param-"+(i-1) : "this";
            String keyVarName = callSiteVarNames[i];
            if(keyVarName == null) continue;
            Set<Node> nodesFromSummary = summaryContainer.getNodesByVarName(key);
            boolean isKeepSave = false;
            for(Node sum:nodesFromSummary){
                Multimap<String, String> actions = sum.genActions(key, true, new HashSet<>(), summaryContainer); // process actions，可以减少遍历所有node
                SemanticUtils.pureActions(actions);
                if(actions.isEmpty()) continue;
//                actions = SemanticUtils.optimize(actions);
                Set<String> actionKeys = actions.keySet().stream()
                        .sorted(Comparator.comparingInt(String::length))
                        .collect(Collectors.toCollection(LinkedHashSet::new));

                for (String left : actionKeys) {
                    MultiAction leftAction = MultiAction.parse(left, callSiteVarNames, stmt, tempContainer, summaryContainer, container);
                    Set<Node> assignNodes = leftAction.getAssignNodes(actions.get(left), sum.getType(), false);
                    leftAction.assign(assignNodes, isKeepSave, false);
                }
                isKeepSave = true;
            }
        }
        tempContainer.getCache().invalidateAll();
        // backtrack return
        if(summaries.containsKey("return") && stmt instanceof AssignStmt as){
            Set<Node> nodesFromSummary = summaryContainer.getNodesByVarName("return");
            boolean isKeepSave = false;
            for(Node sum:nodesFromSummary){
                Multimap<String, String> actions = sum.genActions("return", true, new HashSet<>(), summaryContainer);
                SemanticUtils.pureActions(actions);
                Set<String> actionKeys = actions.keySet().stream().sorted(Comparator.comparingInt(String::length)).collect(Collectors.toCollection(LinkedHashSet::new));
                for (String left : actionKeys) {
                    MultiAction leftAction = MultiAction.parse(left, callSiteVarNames, stmt, tempContainer, summaryContainer, container);
                    if(leftAction.getActions().size() == 1){
                        // return 或者 return<s|a|k|v>
                        Set<Node> assignNodes = leftAction.getAssignNodes(actions.get(left), sum.getType(), false);
                        leftAction.assign(assignNodes, isKeepSave, true);
                    }else{
                        if(actionKeys.size() == 1){
                            // 这个说明actions 只有 return<f>a 这种情况，return本身是非污染的
                            // 需要先在tempcontaner里面加上return
                            Node returnNode = sum.clone();
                            returnNode.resetUid("return", true);
                            returnNode.clearOther();
                            returnNode.setTaints(Sets.newHashSet(Taint.of(-3, "return")));
                            tempContainer.relateToVarTables("return", Sets.newHashSet(returnNode.makeRef()), false);
                            tempContainer.saveAndFresh(returnNode);
                        }
                        // 处理return field
                        Set<Node> assignNodes = leftAction.getAssignNodes(actions.get(left), sum.getType(), false);
                        leftAction.assign(assignNodes, isKeepSave, true);
                    }
                }
                isKeepSave = true;
            }
        }
        return tempContainer;
    }


    @Override
    public void defaultCase(Object v) {
        if (v instanceof Constant c) {
            caseConstant(c);
        } else if (v instanceof InvokeExpr ie) { // 处理右值的函数调用场景
            caseInvokeExpr(ie);
//            simplify(ie); // 不进行污点分析
        }
    }

    public String[] extractVarNames(InvokeExpr ie) {
        int size = ie.getArgCount() + 1;
        Value base = null;
        if (ie instanceof InstanceInvokeExpr iie) {
            base = iie.getBase();
        }
        String[] objects = new String[size];

        if (base != null) {
            objects[0] = SemanticUtils.extractVarName(base);
        } else {
            objects[0] = null;
        }

        for (int i = 0; i < ie.getArgCount(); i++) {
            objects[i + 1] = SemanticUtils.extractVarName(ie.getArg(i));
        }

        return objects;
    }

    public boolean isNotContainsTaintedInfo(List<Set<Integer>> positions){
        Set<Integer> pos = new HashSet<>();
        positions.forEach(p -> pos.addAll(p));
        return pos.size() == 1 && pos.contains(PositionUtils.NOT_POLLUTED_POSITION);
    }

    /**
     * 简化函数涉及变量的传播
     * 简化处理主要是处理函数调用涉及的对象，如函数调用者，函数参数列表，函数返回值
     * 经过简化处理后，上面涉及的对象position都将统一为同一个列表（除常量外）
     *  将当前调用所涉及到的变量进行污染，比如 a.func(p0,p1) 涉及到 a,p0,p1 变量
     *  1. 抽取a,p0,01的污点
     *  2. clone 变量，并将污点值改成所有污点,fakenode
     *  3. 如果需要返回值，则将所有fakenode返回，处理时只会使用fakenode的taintId来替换返回值的变量
     * @param v
     */
    public void simplify(InvokeExpr v) {
        List<ValueBox> valueBoxes = v.getUseBoxes(); // a.func(p0,p1) 返回 a,p0,p1

        Set<Integer> positions = container.getTaintIdsByValueBoxes(valueBoxes); // 获取所有的污点信息
        for (ValueBox vb : valueBoxes) {
            Set<Integer> copied = new HashSet<>(positions);
            Value value = vb.getValue();
            Type vType = value.getType();
            if(vType instanceof PrimType
                    || vType instanceof NullType
                    || value instanceof Constant
            ) continue; // 这些类型都是不可污染的 可以跳过
            Set<Integer> taints = container.getTaintIdsByValue(value);
            if (taints.size() == 1 && taints.contains(PositionUtils.NOT_POLLUTED_POSITION)) continue;
            copied.removeAll(taints);
            if(copied.isEmpty()) continue;

            if (value instanceof InstanceFieldRef ifr) {
                Value base = ifr.getBase();
                SootField field = ifr.getField();
                Set<Node> nodes = container.getNodes(base);
                for (Node node : nodes) {
                    if(node != null && node.isNeedProcessFields()) {
                        Set<NodeRef> refs = new HashSet<>(node.getFields().get(field.getName()));
                        Set<String> types = refs.stream().map(NodeRef::getType).collect(Collectors.toSet());
                        String type = null;
                        if(types.isEmpty()){
                            type = "java.lang.Object";
                        }else{
                            type = types.stream().findFirst().get();
                        }
                        FakeNode fakeNode = FakeNode.of(value, type, copied);
                        node.addFieldNode(field.getName(), fakeNode, new HashSet<>(), container,false);
                        container.save(fakeNode);
                        container.save(node);
                    }
                }
            } else {
                String varName = SemanticUtils.extractVarName(value);
                Set<String> types = container.getVarNodeTypes(value);
                String type = null;
                if(types.isEmpty()){
                    type = "java.lang.Object";
                }else{
                    type = types.stream().findFirst().get();
                }
                FakeNode fakeNode = FakeNode.of(value, type, copied);
                container.relateToVarTablesWithoutRemoval(varName, fakeNode);
                container.save(fakeNode);
            }
        }

        // process return ap
        Type returnType = v.getMethodRef().getReturnType();
        if(!(returnType instanceof VoidType) && stmt instanceof AssignStmt as) {
            Node returnNode = Node.ofReturnNode(as, returnType.toString(), positions);
            container.save(returnNode);
            setResult(Sets.newHashSet(returnNode)); // 处理只会发生在 assign 语句
        }
    }

}
