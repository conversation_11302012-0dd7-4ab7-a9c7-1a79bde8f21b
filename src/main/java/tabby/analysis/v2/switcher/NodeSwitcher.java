package tabby.analysis.v2.switcher;


import lombok.Setter;
import soot.*;
import soot.jimple.*;
import tabby.analysis.v2.container.ValueContainer;
import tabby.analysis.v2.data.Context;
import tabby.analysis.v2.data.Taint;
import tabby.analysis.v2.data.nodes.ArrayNode;
import tabby.analysis.v2.data.nodes.Node;
import tabby.analysis.v2.data.nodes.NodeRef;
import tabby.common.utils.PositionUtils;
import tabby.common.utils.SemanticUtils;
import tabby.config.GlobalConfiguration;
import tabby.core.container.DataContainer;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * @project tabby_ng
 * @since 2025/3/31
 */
@Setter
public class NodeSwitcher extends AbstractJimpleValueSwitch {

    public static String GET_NODE = "GET_NODE";
    public static String CREATE_NODE = "CREATE_NODE";
    public static String GET_OR_CREATE_NODE = "GET_OR_CREATE_NODE";

    public Context context;
    public ValueContainer container;
    public DataContainer dataContainer;
    protected boolean isPrimTypeNeedToCreate = GlobalConfiguration.IS_PRIM_TYPE_NEED_TO_CREATE; // 默认开启prim类型的分析
    private boolean isGet = true;
    private boolean isCreate = true;

    public NodeSwitcher() {}

    public void accept(Context context) {
        this.context = context;
        this.container = context.getContainer();
        this.dataContainer = context.getDataContainer();
    }

    public void setType(String type) {
        if(GET_NODE.equals(type)){
            isGet = true;
            isCreate = false;
        }else if(CREATE_NODE.equals(type)){
            isGet = false;
            isCreate = true;
        }else if(GET_OR_CREATE_NODE.equals(type)){
            isGet = true;
            isCreate = true;
        }
    }



    @Override
    public void defaultCase(Object v) {
        if (v instanceof Constant) {
            caseConstant((Constant) v);
        }
    }

    /**
     * 普通变量类型
     * @param v
     */
    @Override
    public void caseLocal(Local v) {
        Set<Node> nodes = new HashSet<>();
        if (isGet) {
            nodes = container.getNodes(v);
        }

        if (nodes.isEmpty() && isCreate
                && (isPrimTypeNeedToCreate || SemanticUtils.isNecessaryType(v))) {
            Node node = Node.ofNodeWithValue(v);
            node.setName(v.getName());
            node.getTaints().add(Taint.of(PositionUtils.NOT_POLLUTED_POSITION, v.getName()));
            container.save(node);
            container.relateToVarTablesWithRemoval(SemanticUtils.extractVarName(v), node);
            nodes.add(node);
        }

        setResult(nodes);
    }

    /**
     * Class.a
     *
     * @param v
     */
    @Override
    public void caseStaticFieldRef(StaticFieldRef v) {
        Set<Node> nodes = new HashSet<>();
        SootField field = v.getField();
        if (field == null) return;

        if (isGet) {
            nodes = container.getNodes(v);
        }

        if (nodes.isEmpty()
                && isCreate
                && (isPrimTypeNeedToCreate || SemanticUtils.isNecessaryType(v))) {
            Node node = Node.ofNodeWithValue(v);
            node.setName(v.toString());
            node.setIsStatic("1");
            node.getTaints().add(Taint.of(PositionUtils.NOT_POLLUTED_POSITION, v.toString()));
            container.save(node);
            container.relateToVarTablesWithRemoval(SemanticUtils.extractVarName(v), node);
            nodes.add(node);
        }

        setResult(nodes);
    }

    /**
     * a.b
     *
     * @param v
     */
    @Override
    public void caseInstanceFieldRef(InstanceFieldRef v) {
        Set<Node> nodes = new HashSet<>();
        SootField field = v.getField();
        if (field == null) return;

        if (isGet) {
            Value base = v.getBase();
            Set<Node> baseNodes = container.getNodes(base);
            String fieldName = field.getName();
            for (Node node : baseNodes) {
                Collection<NodeRef> refs = node.getFields().get(fieldName);
                refs.forEach(ref -> {
                    Node f = container.getNode(ref);
                    if (f != null) {
                        nodes.add(f);
                    }
                });
            }
        }

        if(nodes.isEmpty()
                && isCreate
                && (isPrimTypeNeedToCreate || SemanticUtils.isNecessaryType(v))){ // 没有找到现成的 field nodes
            Value base = v.getBase();
            Set<Node> baseNodes = container.getNodes(base);
            if(baseNodes.isEmpty()){
                String varName = SemanticUtils.extractVarName(base);
                Node baseNode = Node.ofNodeWithValue(base);
                baseNode.setName(varName);
                baseNode.getTaints().add(Taint.of(PositionUtils.NOT_POLLUTED_POSITION, base.toString()));
                container.save(baseNode);
                container.relateToVarTablesWithRemoval(varName, baseNode);
                baseNodes.add(baseNode);
            }
            // create field
            SootField sootField = v.getField();
            for (Node node : baseNodes) {
                Node fieldNode = Node.ofFieldNode(base.toString(), sootField);
                node.addFieldNode(field.getName(), fieldNode, new HashSet<>(), container,true);
                nodes.add(fieldNode);
                container.save(fieldNode);
                container.save(node);
            }
        }

        setResult(nodes);
    }

    /**
     * a[]
     * a[][]
     * array load
     * @param v
     */
    @Override
    public void caseArrayRef(ArrayRef v) {
        Set<Node> nodes = new HashSet<>();
        // check base value
        Value base = v.getBase();
        if (base == null) return;
        boolean baseNodeExists = false;
        Set<Node> baseNodes = null;
        if (isGet) {
            baseNodes = container.getNodes(base);
            for (Node node : baseNodes) {
                if(node instanceof ArrayNode an){
                    if(!baseNodeExists){
                        baseNodeExists = true;
                    }
                    nodes.addAll(container.getNodesByNodeRefs(an.getArray()));
                }
            }
        }

        if (nodes.isEmpty()
                && isCreate
                && (isPrimTypeNeedToCreate || SemanticUtils.isNecessaryType(v))) {
            Node node = Node.ofNodeWithValue(v);
            container.save(node);
            nodes.add(node);

            if(baseNodeExists){
                for(Node baseNode : baseNodes){
                    if(baseNode instanceof ArrayNode an){
                        an.addArrayNode(node, container, new HashSet<>(), true);
                        container.save(an);
                    }
                }
            }else{
                String varName = SemanticUtils.extractVarName(v);
                ArrayNode arrayNode = (ArrayNode) Node.ofNodeWithValue(base);
                arrayNode.addArrayNode(node, container, new HashSet<>(),true);
                container.save(arrayNode);
                container.relateToVarTablesWithRemoval(varName, arrayNode);
            }
        }

        setResult(nodes);
    }

    /**
     * 静态变量
     * int、double、float、long、Class、String、Null
     * return obj
     *
     * @param v
     */
    public void caseConstant(Constant v) {
        Set<Node> nodes = new HashSet<>();
        if(isGet){
            nodes = container.getNodes(v);
        }

        if(nodes.isEmpty() && isCreate
                && (isPrimTypeNeedToCreate || SemanticUtils.isNecessaryType(v))){
            Node node = Node.ofNodeWithValue(v);
            container.save(node);
            container.relateToVarTablesWithRemoval(SemanticUtils.extractVarName(v), node);
            nodes.add(node);
        }

        setResult(nodes);
    }

    @Override
    public void caseThisRef(ThisRef v) {
        // do nothing
    }

    @Override
    public void caseParameterRef(ParameterRef v) {
        // do nothing
    }
}
