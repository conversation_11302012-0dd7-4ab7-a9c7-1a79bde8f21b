package tabby.analysis.v2.data;


import com.google.common.base.Objects;
import lombok.Getter;

/**
 * 不可修改
 * @project tabby_ng
 * @since 2025/3/27
 */
@Getter
public class TaintType {
    public static TaintType THIS = new TaintType(-1, "this");
    public static TaintType SOURCE = new TaintType(-2, "source");
    public static TaintType NOT_POLLUTED_POSITION = new TaintType(-3, "untainted");
    public static TaintType DAO = new TaintType(-4, "dao");
    public static TaintType RPC = new TaintType(-5, "rpc");
    public static TaintType AUTH = new TaintType(-6, "auth");

    private final int index;
    private final String constant;

    TaintType(int index, String constant) {
        this.index = index;
        this.constant = constant;
    }

    public boolean isThis(){
        return this == THIS;
    }

    public boolean isParam(){
        return this.index >= 0;
    }

    public boolean isSource(){
        return this == SOURCE;
    }

    public static TaintType of(int index){
        switch(index){
            case -1:
                return THIS;
            case -2:
                return SOURCE;
            case -3:
                return NOT_POLLUTED_POSITION;
            case -4:
                return DAO;
            case -5:
                return RPC;
            case -6:
                return AUTH;
            default:
                if(index >=0 ){ // parameters
                    return new TaintType(index, "param");
                }
        }
        return null;
    }

    public static TaintType of(String identity){
        if(identity.equals("this")){
            return THIS;
        }else if(identity.equals("source")){
            return SOURCE;
        }else if(identity.equals("untainted")){
            return NOT_POLLUTED_POSITION;
        }else if(identity.equals("dao")){
            return DAO;
        }else if(identity.equals("rpc")){
            return RPC;
        }else if(identity.equals("auth")){
            return AUTH;
        }else if(identity.contains("-")){
            String[] split = identity.split("-");
            if(split.length == 2){
                return new TaintType(Integer.parseInt(split[1]), "param");
            }
        }

        return null;
    }

    @Override
    public String toString() {
        if(isParam()){
            return constant + "-" + index;
        }else{
            return constant;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof TaintType taintType)) return false;
        return index == taintType.index && Objects.equal(constant, taintType.constant);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(index, constant);
    }
}
