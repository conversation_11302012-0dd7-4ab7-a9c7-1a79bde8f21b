package tabby.analysis.v2.data.nodes;


import com.google.common.collect.Multimap;
import com.google.common.collect.MultimapBuilder;
import lombok.Data;
import tabby.analysis.v2.container.ValueContainer;
import tabby.analysis.v2.data.Taint;
import tabby.common.utils.SemanticUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * just ignore
 * @project tabby_ng
 * @since 2025/4/12
 */
@Data
public class IgnoreNode extends Node {

    private boolean isNeedTransfer = false;

    public IgnoreNode() {
        this.uid = "I" + SemanticUtils.calculateUuid("Tabby-Ignore-Node").substring(0, 8);
        this.type = "null";
    }

    public boolean shallowEquals(Node other){
        return uid.equals(other.uid);
    }

    public static IgnoreNode of(){
        return new IgnoreNode();
    }

    @Override
    public void addFieldNode(String fieldName, Node fieldNode, Set<String> visited, ValueContainer container, boolean isNewCreated) {

    }

    @Override
    public Set<Taint> genChildTaints(String name, String tag) {
        return new HashSet<>();
    }

    @Override
    public Multimap<String, String> genActions(String key, boolean isNeedNewOp, Set<String> visited, ValueContainer container) {
        return MultimapBuilder.hashKeys().hashSetValues().build();
    }
}
