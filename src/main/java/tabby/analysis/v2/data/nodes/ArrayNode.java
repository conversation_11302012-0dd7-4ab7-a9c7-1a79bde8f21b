package tabby.analysis.v2.data.nodes;


import com.google.common.collect.Multimap;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import soot.Value;
import tabby.analysis.v2.container.ValueContainer;
import tabby.analysis.v2.data.Taint;
import tabby.common.utils.SemanticUtils;
import tabby.config.GlobalConfiguration;

import java.util.HashSet;
import java.util.Set;

/**
 * 类型1 Object[] int[] byte[] ......
 * @project tabby_ng
 * @since 2025/3/27
 */
@Getter
@Setter
public class ArrayNode extends Node {

    protected Set<NodeRef> array = new HashSet<>(); // 数组类型的value放在array字段，比如 a[1] = a1; set/list.add(a1);

    public ArrayNode() {
    }

    public ArrayNode(Value value){
        super(value);
    }

    public static Node of(){
        return new ArrayNode();
    }

    public static Node of(Value value){
        Node n = new ArrayNode(value);
        n.uid = "A" + SemanticUtils.calculateUuid(value).substring(0, 8);
        return n;
    }

    public void addArrayNode(Node arrayNode, ValueContainer container, Set<String> visited, boolean isNewCreated){
        if(arrayNode == null || visited.contains(arrayNode.getUid())) return;
        visited.add(arrayNode.getUid());
        if(isNewCreated){
            Set<Taint> childTaints = genChildTaints(null, "a");
            arrayNode.setTaints(childTaints);
        }

        Set<NodeRef> newArray = new HashSet<>(array);
        boolean added = true;
        if(arrayNode.isFake()){
            for(NodeRef ref : newArray){ // 合并fakenode
                Node node = container.getNode(ref);
                if(node == null || !node.isFake()) continue;
                if(node.shallowEquals(arrayNode)){
                    node.union(arrayNode, visited, false, container, container);
                    added = false;
                }
            }
        }

        if(added && array.size() <= GlobalConfiguration.ARRAY_MAX_LENGTH){
            array.add(arrayNode.makeRef());
        }
    }

    public void addArrayNode(Set<NodeRef> refs, ValueContainer container, boolean isNewCreated){
        Set<String> visited = new HashSet<>();
        for(NodeRef ref : refs){
            Node node = container.getNode(ref);
            if(node == null) continue;
            addArrayNode(node, container, visited, isNewCreated);
        }
    }

    public String toCacheString(){
        return String.format("%s[%s]", getIdentity(), type);
    }

    @Override
    public String getIdentity() {
        return "A";
    }

    @Override
    public Set<String> genOthers() {
        Set<String> genArrays = new HashSet<>();
        array.forEach((nodeRef) -> {
            genArrays.add(String.join("|", uid, "ARRAY", nodeRef.getType(), nodeRef.getStaticType(), nodeRef.getUid()));
        });
        return genArrays;
    }

    @Override
    public void recoverOthers(String record) {
        if(record.contains("|ARRAY|")){
            String[] split = record.split("\\|");
            String keyType = split[2];
            String isStatic = split[3];
            String arrayUid = split[4];
            array.add(new NodeRef(arrayUid, keyType, isStatic));
        }
    }

    @Override
    public Multimap<String, String> genActions(String key, boolean isNeedNewOp, Set<String> visited, ValueContainer container) {
        Multimap<String, String> summary = super.genActions(key, isNeedNewOp, visited, container);

        if(!array.isEmpty() && !key.endsWith("<s>")){
            array.forEach((nodeRef) -> {
                if(visited.contains(nodeRef.getUid())) return ;
                visited.add(nodeRef.getUid());
                Node arrayNode = container.getNode(nodeRef);
                if(arrayNode != null){
                    summary.putAll(arrayNode.genActions(key+"<a>", isNeedNewOp, visited, container));
                }
            });
        }

        return summary;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (!(o instanceof ArrayNode arrayNode)) return false;

        return new EqualsBuilder().appendSuper(super.equals(o)).append(array, arrayNode.array).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).appendSuper(super.hashCode()).append(array).toHashCode();
    }

    public boolean shallowEquals(Node other){
        if (this == other) return true;

        if(!(other instanceof ArrayNode an)) return false;

        return new EqualsBuilder()
                .appendSuper(super.shallowEquals(an)).isEquals();
    }

    @Override
    protected void unionOther(Node other, Set<String> visited, ValueContainer container, ValueContainer otherContainer) {
        if(other instanceof ArrayNode an && isArray()){
            Set<NodeRef> otherArray = new HashSet<>(an.getArray());
            otherArray.forEach((nodeRef) -> {
                if(array.contains(nodeRef)){
                    Node arrayNode = container.getNode(nodeRef);
                    Node otherArrayNode = otherContainer.getNode(nodeRef);
                    if(arrayNode != null){
                        arrayNode.union(otherArrayNode, visited, true, container, otherContainer);
                    }
                }else{
                    Node arrayNode = otherContainer.getNode(nodeRef);
                    addArrayNode(arrayNode, container, new HashSet<>(), false);
                    container.copyNodeFromOtherContainer(nodeRef, visited, otherContainer);
                }
            });
        }
    }

    @Override
    public void clearOther() {
        super.clearOther();
        array.clear();
    }
}
