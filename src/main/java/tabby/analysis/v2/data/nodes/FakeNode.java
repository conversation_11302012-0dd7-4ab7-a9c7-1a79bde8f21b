package tabby.analysis.v2.data.nodes;


import com.google.common.collect.Multimap;
import com.google.common.collect.MultimapBuilder;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import soot.Type;
import soot.jimple.Stmt;
import tabby.analysis.v2.container.ValueContainer;
import tabby.analysis.v2.data.Taint;
import tabby.common.utils.PositionUtils;
import tabby.common.utils.SemanticUtils;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用于 simplify 处理成功后的结果
 * 用于 之前<s>的状态表示
 * FakeNode 不应该继续处理field，他应该是copy后的污点集合
 * @project tabby_ng
 * @since 2025/4/9
 */
@Getter
@Setter
public class FakeNode extends Node {

    public static FakeNode of(){
        return new FakeNode();
    }

    public FakeNode(){}

    public static FakeNode of(Stmt stmt, Type type, int taintId){
        FakeNode fakeNode = new FakeNode();
        fakeNode.uid = "F"+ SemanticUtils.calculateUuid(stmt).substring(0,8);
        fakeNode.type = type.toString();
        fakeNode.getTaints().add(Taint.of(taintId, ""));
        return fakeNode;
    }

//    public static FakeNode of(Stmt stmt, Set<String> types, Set<TaintType> taintTypes){
//        FakeNode fakeNode = new FakeNode();
//        fakeNode.uid = "F"+ SemanticUtils.calculateUuid(stmt).substring(0,8);
//        fakeNode.types.addAll(types);
//        fakeNode.taintTypes.addAll(taintTypes);
//        return fakeNode;
//    }

    public static FakeNode of(Object obj, String type, Set<Integer> taintIds){
        FakeNode fakeNode = new FakeNode();
        fakeNode.uid = "F"+ SemanticUtils.calculateUuid(obj).substring(0,8);
        fakeNode.type = type;
        fakeNode.setTaints(
                taintIds.stream()
                        .map(id -> Taint.of(id, ""))
                        .collect(Collectors.toSet()));
        return fakeNode;
    }

    public static FakeNode ofSpecialNode(String tag){
        FakeNode fakeNode = new FakeNode();
        fakeNode.uid = "F"+ SemanticUtils.calculateUuid(tag).substring(0,8);
        int index = PositionUtils.getPosition(tag);
        fakeNode.setTaints(Sets.newHashSet(Taint.of(index, tag)));
        return fakeNode;
    }

    @Override
    public void addFieldNode(String fieldName, Node fieldNode, Set<String> visited, ValueContainer container, boolean isNewCreated) {
        // no need to add field to fake node
    }

    @Override
    public String getIdentity() {
        return "F";
    }

    @Override
    public String genMeta() {
        return String.join("|",
                uid,
                "META",
                type,
                String.join(",", getTaintIds().stream().map(Object::toString).collect(Collectors.toSet()))
        );
    }

    @Override
    public void recoverMeta(String record) {
        String[] split = record.split("\\|");
        uid = split[0];
        type = split[2];
        if(split.length >= 4 && !split[3].isEmpty()){
            taints = Sets.newHashSet(split[3].split(","))
                    .stream().map(id -> Taint.of(Integer.parseInt(id), "")).collect(Collectors.toSet());
        }

    }

    @Override
    public Multimap<String, String> genActions(String key, boolean isNeedNewOp, Set<String> visited, ValueContainer container) {
        Multimap<String, String> summary = MultimapBuilder.hashKeys().hashSetValues().build();
        Set<Integer> positions = getTaintIds();

        if(positions.size() == 1 && positions.contains(PositionUtils.NOT_POLLUTED_POSITION)){
            // pass
        }else {
            for(int taintId:positions){
                String taintStr = PositionUtils.getPosition(taintId);
                summary.put(key+"<s>", taintStr);
            }
            summary.remove(key+"<s>", SemanticUtils.cleanEndTag(key));
            summary.remove(key+"<s>", PositionUtils.UNTAITED_STRING);
        }
        return summary;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (!(o instanceof FakeNode fakeNode)) return false;

        return new EqualsBuilder().append(uid, fakeNode.uid).append(type, fakeNode.type).append(taints, fakeNode.taints).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(uid).append(type).append(taints).toHashCode();
    }

    @Override
    public boolean shallowEquals(Node other) {
        if(!(other instanceof FakeNode)) return false;

        return super.shallowEquals(other);
    }

    @Override
    protected void unionOther(Node other, Set<String> visited, ValueContainer container, ValueContainer otherContainer) {

    }

    @Override
    protected void unionFields(Node other, Set<String> visited, ValueContainer container, ValueContainer otherContainer) {

    }
}
