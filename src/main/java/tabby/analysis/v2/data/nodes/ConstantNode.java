package tabby.analysis.v2.data.nodes;


import com.google.common.collect.Multimap;
import com.google.common.collect.MultimapBuilder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import soot.Value;
import soot.jimple.Constant;
import tabby.analysis.v2.container.ValueContainer;
import tabby.analysis.v2.data.Taint;
import tabby.common.utils.PositionUtils;
import tabby.common.utils.SemanticUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * @project tabby_ng
 * @since 2025/3/27
 */
@Getter
@Setter
public class ConstantNode extends Node {

    protected String value;

    public ConstantNode() {
    }

    public ConstantNode(Value value) {
        super(value);
    }

    public static Node of(){
        return new ConstantNode();
    }

    public static Node of(Value value){
        ConstantNode n = new ConstantNode(value);
        n.uid = "C" + SemanticUtils.calculateUuid(value).substring(0, 8);
        if(value instanceof Constant c){
            n.value = value.toString();
        }else{
            n.value = SemanticUtils.extractVarName(value);
        }
        return n;
    }

    public void apply(Constant constant){
        value = constant.toString();
        sootType = constant.getType();
        type = sootType.toString();
    }

    @Override
    public void addFieldNode(String fieldName, Node fieldNode, Set<String> visited, ValueContainer container, boolean isNewCreated) {
        // no need to create field
    }

    @Override
    public Set<Taint> genChildTaints(String name, String tag) {
        // no need to create child taint
        return new HashSet<>();
    }

    @Override
    protected void unionFields(Node other, Set<String> visited, ValueContainer container, ValueContainer otherContainer) {
        // no need to union fields
    }

    @Override
    public Set<String> genOthers() {
        Set<String> genValue = new HashSet<>();
        genValue.add(String.join("|", uid, "CONSTANT", value));
        return genValue;
    }

    @Override
    public void recoverOthers(String record) {
        if(record.contains("|CONSTANT|")){
            String[] split = record.split("\\|");
            value = split[2];
        }
    }

    @Override
    public Multimap<String, String> genActions(String key, boolean isNeedNewOp, Set<String> visited, ValueContainer container) {
        Multimap<String, String> summary = MultimapBuilder.hashKeys().hashSetValues().build();
        if(Taint.isContainsTaint(taints, key)){
            for(Taint taint : taints){
                if(taint.isContainTaint()){
                    summary.put(key, taint.getFrom().isEmpty()? PositionUtils.getPosition(taint.getIndex()):taint.getFrom());
                }
            }
            summary.remove(key, SemanticUtils.cleanEndTag(key));
        }else if(isNeedNewOp && Taint.isNotContainTaint(taints)){
            // 有可能在函数内部发生了替换的操作
            summary.put(key, "newed"); // 在summary处理的地方也要处理这个
        }
        return summary;
    }

    @Override
    public String getIdentity() {
        return "C";
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (!(o instanceof ConstantNode that)) return false;

        return new EqualsBuilder().appendSuper(super.equals(o)).append(value, that.value).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).appendSuper(super.hashCode()).append(value).toHashCode();
    }

    public boolean shallowEquals(Node other){
        if (this == other) return true;

        if(!(other instanceof ConstantNode cn)) return false;

        return new EqualsBuilder()
                .appendSuper(super.shallowEquals(cn)).append(value, cn.value).isEquals();
    }
}
