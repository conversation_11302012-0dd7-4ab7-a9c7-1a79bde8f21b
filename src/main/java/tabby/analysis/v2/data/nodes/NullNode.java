package tabby.analysis.v2.data.nodes;


import com.google.common.collect.Multimap;
import com.google.common.collect.MultimapBuilder;
import tabby.analysis.v2.container.ValueContainer;
import tabby.analysis.v2.data.Taint;
import tabby.common.utils.SemanticUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * @project tabby_ng
 * @since 2025/3/27
 */
public class NullNode extends Node {

    public static final NodeRef INSTANCE_REF = new NullNode().makeRef();

    public NullNode() {
        this.uid = "O" + SemanticUtils.calculateUuid("Tabby-Null-Node").substring(0, 8);
        this.type = "null";
    }

    public static Node of(){
        return new NullNode();
    }

    @Override
    public void addFieldNode(String fieldName, Node fieldNode, Set<String> visited, ValueContainer container, boolean isNewCreated) {

    }

    @Override
    public Set<Taint> genChildTaints(String name, String tag) {
        return new HashSet<>();
    }

    @Override
    protected void unionFields(Node other, Set<String> visited, ValueContainer container, ValueContainer otherContainer) {

    }

    @Override
    public Multimap<String, String> genActions(String key, boolean isNeedNewOp, Set<String> visited, ValueContainer container) {
        return MultimapBuilder.hashKeys().hashSetValues().build();
    }

    public boolean shallowEquals(Node other){
        return other.uid.equals(uid);
    }


    @Override
    public void setType(String type) {
        // 不能修改
    }

    @Override
    public void setFields(Multimap<String, NodeRef> fields) {

    }

    @Override
    public Multimap<String, NodeRef> getFields() {
        return MultimapBuilder.hashKeys().hashSetValues().build();
    }

    @Override
    public void setTaints(Set<Taint> taints) {

    }

    @Override
    public Set<Taint> getTaints() {
        return new HashSet<>();
    }
}
