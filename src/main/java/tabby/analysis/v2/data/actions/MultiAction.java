package tabby.analysis.v2.data.actions;


import lombok.Getter;
import soot.SootField;
import soot.jimple.Stmt;
import tabby.analysis.v2.container.ValueContainer;
import tabby.analysis.v2.data.Taint;
import tabby.analysis.v2.data.nodes.ArrayNode;
import tabby.analysis.v2.data.nodes.FakeNode;
import tabby.analysis.v2.data.nodes.MapNode;
import tabby.analysis.v2.data.nodes.Node;
import tabby.common.utils.PositionUtils;
import tabby.common.utils.SemanticUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @project tabby_ng
 * @since 2025/4/23
 */
@Getter
public class MultiAction {

    private List<Action> actions = new LinkedList<>();
    private Stmt stmt;
    private String[] callSiteVarNames;
    private ValueContainer newContainer;
    private ValueContainer summaryContainer;
    private ValueContainer readOnlyContainer;

    public static MultiAction parse(String action, String[] callSiteVarNames, Stmt stmt, ValueContainer newContainer, ValueContainer summaryContainer, ValueContainer readOnlyContainer){
        String[] split = action.split("<f>");
        MultiAction multiAction = new MultiAction();
        boolean flag = true;
        for(String s : split){
            if(s.contains("><")){ // 可能存在混杂 <a><k><v> 之类的情况，按顺序把这些填上
                int index = s.indexOf("<");
                String actionName = s.substring(0,index);
                List<String> tags = getTags(s);
                Action lastAction = Action.of(String.format("%s", actionName), callSiteVarNames, flag);
                multiAction.actions.add(lastAction);
                for(String tag : tags){
                    if("s".equals(tag)){
                        lastAction.isOnlyChangeTaints = true;
                    }else{
                        Action act = Action.of(String.format("<%s>", tag), callSiteVarNames, flag);
                        act.isSpecialAction = true;
                        multiAction.actions.add(act);
                        lastAction = act;
                    }
                }
            }else{
                Action act = Action.of(s, callSiteVarNames, flag);
                multiAction.actions.add(act);
            }

            if(flag){
                flag = false;
            }
        }
        multiAction.callSiteVarNames = callSiteVarNames;
        multiAction.newContainer = newContainer;
        multiAction.summaryContainer = summaryContainer;
        multiAction.readOnlyContainer = readOnlyContainer;
        multiAction.stmt = stmt;
        return multiAction;
    }

    public static List<String> getTags(String action){
        Pattern pattern = Pattern.compile("<([^<>]*)>");
        List<String> tags = new LinkedList<>();
        Matcher matcher = pattern.matcher(action);
        while(matcher.find()){
            String matched = matcher.group(1);
            if(matched != null){
                tags.add(matched);
            }
        }
        return tags;
    }

    public void assign(Set<Node> nodes, boolean isKeepSave, boolean isNeedFromNewContainer){
        if(nodes == null || nodes.isEmpty()) return;
        Action tailAction = getTailAction();
        if(tailAction.isOnlyChangeTaints){
            // create fake node
            Set<Integer> positions = readOnlyContainer.getTaintIdsByNodes(nodes);
            if(positions.size() >= 2) {
                positions.remove(-3);
            }
            FakeNode fn = FakeNode.of(getHeadAction().actionName+".*."+getTailAction().actionName, "java.lang.Object", positions);
            nodes.clear();
            nodes.add(fn);
        }
        // copy to newContainer
        boolean isStatic = false;
        for(Node node : nodes){
            if(!(node instanceof FakeNode)){
                isStatic = node.isStatic();
            }
            newContainer.saveIfAbsent(node);
        }

        if(actions.size() == 1 && !(getHeadAction().isArrayOp() || getHeadAction().isMapKeyOp() || getHeadAction().isMapValueOp())){ // 没有field参与，直接修改var表
            if(getHeadAction().isReturnAction){
                newContainer.assign(tailAction.actionName, nodes, isStatic, !tailAction.isOnlyChangeTaints && !isKeepSave);
            }
//            else if(getHeadAction().isOnlyChangeTaints){
//                Set<Node> leftNodes = getNodes(true, true, isNeedFromNewContainer);
//                if(leftNodes.isEmpty()){
//                    newContainer.assign(tailAction.varName, nodes, isStatic, !tailAction.isOnlyChangeTaints && !isKeepSave);
//                }else{
//                    for(Node leftNode : leftNodes){
//                        if(leftNode == null) continue;
//                        leftNode.getTaints().addAll(nodes.stream().findFirst().get().getTaints());
//                        newContainer.save(leftNode);
//                    }
//                }
//            }
            else {
                newContainer.assign(tailAction.varName, nodes, isStatic, !tailAction.isOnlyChangeTaints && !isKeepSave);
            }
        }else {
            Set<Node> leftNodes = getNodes(false, true, isNeedFromNewContainer); // from newContainer
            // assign
            for(Node node : leftNodes){
                if(node == null) continue;

                if(tailAction.isArrayOp() && node instanceof ArrayNode an){ // 有field参与
                    nodes.forEach(n -> {
                        an.addArrayNode(n, newContainer, new HashSet<>(), false);
                    });
                }else if(tailAction.isMapKeyOp() && node instanceof MapNode mn){
                    nodes.forEach(n -> {
                        mn.addKeyNode(n, newContainer, new HashSet<>(),false);
                    });
                }else if(tailAction.isMapValueOp() && node instanceof MapNode mn){
                    nodes.forEach(n -> {
                        mn.addValueNode(n, newContainer, new HashSet<>(),false);
                    });
                }else if(node.isNeedProcessFields()){
                    if(tailAction.isOnlyChangeTaints || isKeepSave){
                        nodes.forEach(n -> {
                            node.addFieldNode(tailAction.actionName, n, new HashSet<>(), newContainer, false);
                        });
                    }else{
                        node.replaceFieldNodes(tailAction.actionName, nodes, newContainer);
                    }
                }else if(tailAction.isOnlyChangeTaints || node.isFake() || node.isConstant()){
                    // 对于node是fake、constant类型，且需要<s>的情况，合并污点
                    Set<Integer> positions = readOnlyContainer.getTaintIdsByNode(node);
                    if(positions.size() == 1 && positions.contains(-3)){
                        // pass
                    }else{
                        node.getTaints().addAll(positions.stream().map(p -> Taint.of(p, "")).toList());
                    }
                }// 其他情况忽略
                // update to newContainer
                newContainer.save(node);
            }
        }
        // end
    }

    public Set<Node> getAssignNodes(Collection<String> rights, String sumType, boolean isNeedFromNewContainer){
        List<String> sortedValues = rights.stream()
                .sorted(Comparator.comparingInt(String::length))
                .toList();
        int size = sortedValues.size();
        Set<Node> assignNodes = new HashSet<>();
        for (String right : sortedValues) {
            if(size > 1 && "newed".equals(right)) continue;
            if("newed".equals(right)){
                if(summaryContainer != null){
                    Set<Node> nodes = getNodesFromSummary();
                    for(Node node : nodes){
                        Node copied = node.clone();
                        copied.resetUid("newed"+stmt, false);
                        copied.clearOther();
                        assignNodes.add(copied);
                    }
                }
            }else if(right.startsWith("param-") && right.contains("..")){
                // 特殊的规则 param-1..n 表示从第二个参数开始后面的都汇聚起来
                // 不会有
                String[] split = right.split("\\.\\.");
                int taintId = PositionUtils.getPosition(split[0]) + 1;
                for(int i = taintId; i < callSiteVarNames.length; i++){
                    String varName = callSiteVarNames[i];
                    if(varName != null){
                        Set<Node> nodes = readOnlyContainer.getNodesByVarName(varName);
                        assignNodes.addAll(nodes);
                    }
                }
            }
            else if(PositionUtils.isSpecialTag(right)){ // 特殊obj
                Node node = FakeNode.ofSpecialNode(right);
                assignNodes.add(node);
            } else {
                MultiAction rightAction = MultiAction.parse(right, callSiteVarNames, stmt, newContainer, summaryContainer, readOnlyContainer);
                Set<Node> nodes = rightAction.getNodes(true, true, isNeedFromNewContainer);
                assignNodes.addAll(nodes);
            }
        }
        return assignNodes;
    }

    public Set<Node> getNodesFromSummary(){
        Set<Node> nodesFromSummary = new HashSet<>();
        Action lastAction = null;
        for(Action act:actions){
            if(lastAction == null){
                nodesFromSummary = act.getNodes(act.actionName, true, summaryContainer);
            }else{
                nodesFromSummary = act.getNodes(nodesFromSummary, true, summaryContainer);
            }
            lastAction = act;
            if(nodesFromSummary.isEmpty()){
                break; // 找不到了
            }
        }
        // 没有新增逻辑，如果没有找到就是没有
        return nodesFromSummary;
    }

    public Set<Node> getNodes(boolean isNeedComplete, boolean isNeedRecoverTaint, boolean isNeedFromNewContainer){
        Set<Node> baseNodes = new HashSet<>();
        Set<Node> nodesFromSummary = new HashSet<>();
        Set<Node> preNodes = new HashSet<>();
        Action lastAction = null;
        ValueContainer valueFromContainer = isNeedFromNewContainer || !isNeedComplete ? newContainer : readOnlyContainer;
        for(Action act : actions){ // 根据解析后的actions，找到对应的nodes
            if(!isNeedComplete && act.equals(getTailAction()) && !(act.isArrayOp() || act.isMapKeyOp() || act.isMapValueOp())) continue; // 对于field的最后一个字段不应该被提取
            if(!isNeedComplete && act.equals(getTailAction()) && act.isSpecialAction) continue;
            if(lastAction == null){
                // head action
                String name = act.isReturnAction?"return":null;
                baseNodes = act.getNodes(name, isNeedComplete, valueFromContainer);
                if(summaryContainer != null && isNeedRecoverTaint){
                    nodesFromSummary = act.getNodes(act.actionName, isNeedComplete, summaryContainer);
                }
            }else{
                // field action
                baseNodes = act.getNodes(baseNodes, isNeedComplete, valueFromContainer);
                if(summaryContainer != null && isNeedRecoverTaint){
                    nodesFromSummary = act.getNodes(nodesFromSummary, isNeedComplete, summaryContainer);
                }
            }
            lastAction = act;
            if(baseNodes.isEmpty()){
                if(nodesFromSummary.isEmpty()){
                    // 直接创建一个
                    for(Node preNode:preNodes){
                        if(preNode instanceof FakeNode) continue;
                        if(preNode != null) {
                            if(lastAction.isSpecialAction && preNode instanceof ArrayNode an){
                                String type = "java.lang.Object";
                                if(an.getType().contains("[]")){
                                    type = an.getType().replace("[]", "");
                                }
                                Node fieldNode = Node.ofFieldNode(preNode.getName(), "<a>");
                                fieldNode.setType(type);
                                Set<Taint> taints = preNode.genChildTaints(act.actionName, "<a>");
                                fieldNode.setTaints(taints);
                                an.addArrayNode(fieldNode, readOnlyContainer, new HashSet<>(), true);
                                baseNodes.add(fieldNode);
                            }else if(lastAction.isSpecialAction && preNode instanceof MapNode mn){
                                String tag = lastAction.isMapKeyOp()?"<k>":"<v>";
                                Node fieldNode = Node.ofFieldNode(preNode.getName(), tag);
                                Set<Taint> taints = preNode.genChildTaints(act.actionName, tag);
                                fieldNode.setTaints(taints);
                                if(lastAction.isMapKeyOp()){
                                    mn.addKeyNode(fieldNode, readOnlyContainer, new HashSet<>(), true);
                                }else if(lastAction.isMapValueOp()){
                                    mn.addValueNode(fieldNode, readOnlyContainer, new HashSet<>(), true);
                                }
                                baseNodes.add(fieldNode);
                                readOnlyContainer.saveAndFresh(fieldNode);
                                readOnlyContainer.saveAndFresh(preNode);
                            } else {
                                String type = preNode.getType();
                                SootField field = SemanticUtils.getField(type, act.actionName);
                                Node fieldNode = null;
                                if(field != null){
                                    fieldNode = Node.ofFieldNode(preNode.getName(), field);
                                }else{
                                    fieldNode = Node.ofFieldNode(preNode.getName(), act.actionName);
                                }
                                preNode.addFieldNode(lastAction.actionName, fieldNode, new HashSet<>(), readOnlyContainer,true);
                                readOnlyContainer.saveAndFresh(fieldNode);
                                readOnlyContainer.saveAndFresh(preNode);
                                baseNodes.add(fieldNode);
                            }
                        }
                    }
                }else{ // 用summary的数据来创建
                    if(preNodes.isEmpty()){ // 说明第一个节点也找不到，理论上不存在这种情况，但是也写一下处理
                        for(Node node : nodesFromSummary){
                            if(node != null){
                                Node copied = node.clone(); // 克隆只是为了方便创建，不继承原有taint和field
                                copied.clearOther();

                                Set<Node> nodes = null;
                                if((lastAction.isArrayOp() || lastAction.isMapKeyOp() || lastAction.isMapValueOp())
                                        && lastAction.varName != null){
                                    nodes = readOnlyContainer.getNodesByVarName(lastAction.varName);
                                }

                                if(nodes == null){
                                    String varName = lastAction.varName;
                                    Set<Integer> taintedIds = readOnlyContainer.getTaintIdsByVarName(varName);
                                    if(taintedIds.isEmpty()){
                                        taintedIds.add(PositionUtils.NOT_POLLUTED_POSITION);
                                    }
                                    copied.setTaints(taintedIds.stream().map(taintedId -> Taint.of(taintedId, varName)).collect(Collectors.toSet()));
                                    readOnlyContainer.saveAndFresh(copied);
                                    String name = lastAction.varName == null ? lastAction.actionName : lastAction.varName;
                                    boolean isRemoval = lastAction.varName != null;
                                    readOnlyContainer.assign(name, copied, node.isStatic(), isRemoval);
                                    baseNodes.add(copied);
                                }else{
                                    for(Node preNode : nodes){
                                        if(preNode == null) continue;

                                        copied.resetUid(preNode.getName()+lastAction.actionName, true);
                                        if(lastAction.isArrayOp() && preNode instanceof ArrayNode an){
                                            an.addArrayNode(copied, readOnlyContainer, new HashSet<>(), true);
                                        }else if(lastAction.isMapKeyOp() && preNode instanceof MapNode m){
                                            m.addKeyNode(copied, readOnlyContainer, new HashSet<>(), true);
                                        }else if(lastAction.isMapValueOp() && preNode instanceof MapNode m){
                                            m.addValueNode(copied, readOnlyContainer, new HashSet<>(), true);
                                        }else{
                                            continue;
                                        }

                                        readOnlyContainer.saveAndFresh(copied);
                                        readOnlyContainer.saveAndFresh(preNode);
                                        if(!lastAction.equals(getTailAction())){
                                            // 中间节点如果出现不存在并且新增的情况，那么也要保存一份到newContainer
                                            // tail节点则在外面保存就好
                                            newContainer.saveAndFresh(preNode); // 要覆盖原有的
                                        }
                                        baseNodes.add(copied);
                                    }
                                }
                            }
                        }
                    }else{
                        for(Node preNode : preNodes){
                            // 虽然preNodes来源readOnlyContainer，但是这里的修改是可接受的
                            if(preNode == null) continue;

                            for(Node node : nodesFromSummary){
                                if(node != null){
                                    Node copied = node.clone(); // 克隆只是为了方便创建，不继承原有taint和field
                                    copied.clearOther();
                                    copied.resetUid(preNode.getName()+lastAction.actionName, true);
                                    if(lastAction.isSpecialAction){
                                        if(lastAction.isArrayOp() && preNode instanceof ArrayNode an){
                                            an.addArrayNode(copied, readOnlyContainer, new HashSet<>(), true);
                                        }else if(lastAction.isMapKeyOp() && preNode instanceof MapNode m){
                                            m.addKeyNode(copied, readOnlyContainer, new HashSet<>(), true);
                                        }else if(lastAction.isMapValueOp() && preNode instanceof MapNode m){
                                            m.addValueNode(copied, readOnlyContainer, new HashSet<>(), true);
                                        }
                                    }else{
                                        preNode.addFieldNode(lastAction.actionName, copied, new HashSet<>(), readOnlyContainer, true);
                                    }
                                    readOnlyContainer.saveAndFresh(copied);
                                    readOnlyContainer.saveAndFresh(preNode);
                                    if(!lastAction.equals(getTailAction())){
                                        // 中间节点如果出现不存在并且新增的情况，那么也要保存一份到newContainer
                                        // tail节点则在外面保存就好
                                        newContainer.saveAndFresh(preNode); // 要覆盖原有的
                                    }
                                    baseNodes.add(copied);
                                }
                            }
                        }
                    }
                }

                // 可能因为fake node中断了，也可能是当前container没有summary的数据
            }

            if(!baseNodes.isEmpty()){
                preNodes.clear();
                preNodes.addAll(baseNodes);
            }else if(!lastAction.equals(getTailAction())){
                break; // 中断了，直接结束
            }
        }

        return baseNodes;
    }

    public Action getHeadAction(){
        if(actions.isEmpty()) return null;
        return actions.get(0);
    }

    public Action getTailAction(){
        if(actions.isEmpty()) return null;
        return actions.get(actions.size() - 1);
    }

    public void addAction(Action action){
        actions.add(action);
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        for(Action act : actions){
            builder.append(act.toString());
            if(!act.equals(getTailAction()) && !act.isSpecialAction){
                builder.append("<f>");
            }
        }
        return builder.toString();
    }
}
