package tabby.analysis.v2.data.actions;


import tabby.analysis.v2.container.ValueContainer;
import tabby.analysis.v2.data.Taint;
import tabby.analysis.v2.data.nodes.ArrayNode;
import tabby.analysis.v2.data.nodes.FakeNode;
import tabby.analysis.v2.data.nodes.Node;
import tabby.analysis.v2.data.nodes.NodeRef;
import tabby.common.utils.PositionUtils;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @project tabby_ng
 * @since 2025/4/21
 */
public class ArrayAction extends Action {

    public ArrayAction(String action, String[] callSiteVarNames) {
        this(action, callSiteVarNames, true);
    }

    public ArrayAction(String action, String[] callSiteVarNames, boolean isNeedParseVarName) {
        super(action);
        actionName = actionName.replace("<a>", "");
        if(isNeedParseVarName && !actionName.isEmpty()){
            int index = PositionUtils.getPosition(actionName)+1;
            if(index >= 0 && index < callSiteVarNames.length){
                varName = callSiteVarNames[index];
            }
        }
    }


    @Override
    public Set<NodeRef> getRefs(String var, boolean isNeedComplete, ValueContainer container) {
        Set<NodeRef> refs = new HashSet<>();
        Set<Node> arrayNodes = container.getNodesByVarNames(var, varName);
        if(isNeedComplete){
            for(Node node : arrayNodes){
                if(node instanceof ArrayNode ar){
                    refs.addAll(ar.getArray());
                }else if(node instanceof FakeNode){
                    refs.add(node.makeRef());
                }
            }
        }else{
            refs.addAll(arrayNodes.stream().map(Node::makeRef).collect(Collectors.toSet()));
        }
        return refs;
    }

    @Override
    public Set<Node> getNodes(Set<Node> baseNodes, boolean isNeedComplete, ValueContainer container) {
        if(isSpecialAction){
            Set<Node> nodes = new HashSet<>();
            for(Node node : baseNodes){
                if(node instanceof ArrayNode an){
                    Set<NodeRef> refs = an.getArray();
                    for(NodeRef ref : refs){
                        Node n = container.getNode(ref);
                        nodes.add(n);
                    }
                }else if(node instanceof FakeNode){
                    nodes.add(node);
                }
            }
            return nodes;
        }else{
            Set<Node> fields = super.getNodes(baseNodes, isNeedComplete, container);
            if(isNeedComplete){
                Set<Node> ret = new HashSet<>();
                for(Node field : fields){
                    if(field instanceof ArrayNode an){
                        ret.addAll(container.getNodesByNodeRefs(an.getArray()));
                    }else if(field instanceof FakeNode){
                        ret.add(field);
                    }
                }
                return ret;
            }else{
                return fields;
            }
        }
    }

    @Override
    public void assign(Set<NodeRef> assignRefs, ValueContainer container, ValueContainer readOnlyContainer) {
        if(actionName != null){
            Set<Taint> taints = new HashSet<>();
            if(isOnlyChangeTaints){
                NodeRef fakeRef = assignRefs.iterator().next();
                Node fakeNode = container.getNode(fakeRef);
                if(fakeNode != null){
                    taints.addAll(fakeNode.getTaints());
                }
            }
            Set<Node> nodes = container.getNodesByVarName(actionName);
            for(Node node : nodes){
                if(node == null) continue;
                if(node instanceof ArrayNode an){
                    if(isOnlyChangeTaints){
                        for(NodeRef ref:an.getArray()){
                            Node arrayNode = container.getNode(ref);
                            if(arrayNode != null){
                                arrayNode.getTaints().addAll(taints);
                            }
                        }
                    }else{
                        an.addArrayNode(assignRefs, container, false);
                    }
                }else if(node instanceof FakeNode fn){
                    Set<Integer> taintIds = readOnlyContainer.getTaintIdsByNodeRefs(assignRefs);
                    fn.getTaints().addAll(taintIds.stream()
                            .map(id -> Taint.of(id, ""))
                            .collect(Collectors.toSet()));
                }
            }
        }
    }

    @Override
    public Set<NodeRef> assign(Node returnNode, Set<NodeRef> assignRefs, ValueContainer container, ValueContainer readOnlyContainer) {
        Set<NodeRef> refs = new HashSet<>();
        if(isReturnAction){
            if(isOnlyChangeTaints){
                // 不应该出现 return<a><s> 的情况
            }else if(returnNode instanceof ArrayNode an){
                an.getArray().addAll(assignRefs);
                container.save(returnNode);
                refs.add(returnNode.makeRef());
            }
        }
        return refs;
    }

    @Override
    public String toString() {
        return actionName+"<a>";
    }
}
