package tabby.analysis.v2;

import com.google.common.collect.Multimap;
import com.google.common.collect.MultimapBuilder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import soot.Body;
import soot.SootMethod;
import soot.Unit;
import soot.jimple.Stmt;
import soot.toolkits.graph.BriefUnitGraph;
import soot.toolkits.graph.UnitGraph;
import soot.toolkits.scalar.ForwardFlowAnalysis;
import tabby.analysis.common.MethodContext;
import tabby.analysis.v2.container.ValueContainer;
import tabby.analysis.v2.data.Context;
import tabby.analysis.v2.data.nodes.Node;
import tabby.analysis.v2.data.nodes.NodeRef;
import tabby.analysis.v2.model.CallEdgeBuilder;
import tabby.analysis.v2.switcher.StmtSwitcher;
import tabby.common.bean.edge.Call;
import tabby.common.bean.ref.MethodReference;
import tabby.common.utils.SemanticUtils;
import tabby.config.GlobalConfiguration;
import tabby.core.container.DataContainer;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/26
 */
@Setter
@Getter
@Slf4j
public class CachedPointerAnalysis extends ForwardFlowAnalysis<Unit, ValueContainer> implements AutoCloseable {

    private Context context;
    private Node retValue = null;
    private StmtSwitcher stmtSwitcher = new StmtSwitcher();
    private CallEdgeBuilder builder = null;
    private Set<String> initialedCallEdge = new HashSet<>();
    private Map<String, Integer> triggerTimes = new HashMap<>();
    /**
     * 当另一个线程已经分析完了，这里不需要保存当前分析的call site
     * 将 isNormalExit 设置为 false, 将不保存call edges
     */
    private boolean isNormalExit = true;
    private Set<Call> callEdges = new HashSet<>();
    private Multimap<String, Multimap<String, String>> actions =
            MultimapBuilder.hashKeys().hashSetValues().build();
    public static Map<String, MethodContext> runningMethods = Collections.synchronizedMap(new HashMap<>());

    /**
     * Construct the analysis from a DirectedGraph representation of a Body.
     *
     * @param graph
     */
    public CachedPointerAnalysis(UnitGraph graph) {
        super(graph);
    }

    @Override
    protected void flowThrough(ValueContainer in, Unit d, ValueContainer out) {
        // 多线程的情况下，先去判断当前method是否已经由其他线程分析完了，如果分析完了就不再继续分析
        if (context.isForceStop() || context.isAnalyseTimeout() ||
                GlobalConfiguration.GLOBAL_FORCE_STOP) {
            // 程序分析因为一些原因导致死循环 但仍然保存call边
            // 或 OOM 直接结束所有分析
            return;
        }

        if (context.getMethodReference().isInitialed()) {
            // 多个线程同时分析一个函数，且当前线程落后其他线程，则直接跳过后续的分析，且不保存当前分析所得的调用边
            isNormalExit = false;
            return;
        }

        if (context.isTimeout()) { // 如果当前函数分析超多最大限时，则停止分析当前函数
            context.setAnalyseTimeout(true);
            isNormalExit = false; // 下一次会重新分析，这里先不保存call边
            return;
        }

//        String debugSig = "<java.util.regex.Pattern: java.util.regex.Pattern$Node expr(java.util.regex.Pattern$Node)>";
//        if(debugSig.equals(context.getMethodSignature())){
//            System.out.println(1);
//        }

        // try to analysis next stmt
        copy(in, out);
        context.accept(out);
        stmtSwitcher.accept(context);
        stmtSwitcher.accept((Stmt) d);
        stmtSwitcher.setTriggerTimes(triggerTimes);
        d.apply(stmtSwitcher); // TODO debug
        out.getCache().invalidateAll(); // 保存下所有需要保存的内容

//        if(debugSig.equals(context.getMethodSignature())){
//            System.out.println("\n");
//            diff(in, out);
//            System.out.println(context.getMethodSignature());
//            System.out.println(d);
//        }
    }

    @Override
    protected ValueContainer newInitialFlow() {
        return new ValueContainer(context.getDataContainer());
    }

    @Override
    protected void merge(ValueContainer in1, ValueContainer in2, ValueContainer out) {
        copy(in1, out);
        // out = in1 + in2
        out.union(in2);
    }

    @Override
    protected void copy(ValueContainer source, ValueContainer dest) {
        dest.copy(source);
    }

    public void doAnalysis() {
        super.doAnalysis();
    }

    public void doEnd() {
        MethodReference ref = context.getMethodReference();
        context.setNormalExit(!context.isForceStop()); // 如果分析过程中出现了死循环，下次不再分析，用simplify处理
        ref.setContainSomeError(context.isForceStop());

        if (isNormalExit || !GlobalConfiguration.IS_NEED_ADD_TO_TIMEOUT_LIST) {
            // 对于到达死循环结束条件的情况 也认为是正常推出
            // 对于timeout的情况，如果是第二次timeout了则直接保存边结果
            context.getDataContainer().store(stmtSwitcher.getCallEdges(), false);
            if(!ref.isActionInitialed()){
                ref.setSummaries(context.getSummaries());
                generateActions(ref);
            }
        }

        if (context.isAnalyseTimeout()) {
            if (context.isTopContext()) {
                context.getDataContainer().getAnalyseTimeoutMethodSigs().add(context.getMethodSignature());
            } else {
                Context preContext = context.getPreContext();
                preContext.setAnalyseTimeout(true);
            }
        }

        ref.setRunning(false);
    }

    public void generateActions(MethodReference ref) {
        if(ref.getRuleActions().isEmpty() && !ref.getSummaries().isEmpty()){
            ValueContainer valueContainer = new ValueContainer(ref.getSummaries(), context.getDataContainer());
            int size = context.getCopiedCurObjects().length;
            for(int i = 0; i < size; i++){
                if(i == 0){
                    Set<Node> nodes = valueContainer.getNodesByVarName("this");
                    for(Node n : nodes){
                        Multimap<String, String> temp = n.genActions("this", true, new HashSet<>(), valueContainer);
                        SemanticUtils.pureActions(temp);
                        ref.addActions(temp);
                    }
                }else{
                    Set<Node> nodes = valueContainer.getNodesByVarName("param-"+(i-1));
                    for(Node n : nodes){
                        Multimap<String, String> temp = n.genActions("param-"+(i-1), true, new HashSet<>(), valueContainer);
                        SemanticUtils.pureActions(temp);
                        ref.addActions(temp);
                    }
                }
            }
            Set<Node> nodes = valueContainer.getNodesByVarName("return");
            for(Node n : nodes){
                Multimap<String, String> temp = n.genActions("return", true, new HashSet<>(), valueContainer);
                SemanticUtils.pureActions(temp);
                ref.addActions(temp);
            }
        }
    }

    public static void makeDefault(
            UnitGraph graph, Context context) {
        MethodReference methodReference = context.getMethodReference();
        // initial
        methodReference.setCallCounter(0);
        // do analysis
        try (CachedPointerAnalysis analysis = new CachedPointerAnalysis(graph)) {
            analysis.setContext(context);
            analysis.setBuilder(new CallEdgeBuilder());
            analysis.doAnalysis();
            analysis.doEnd();
            if (!context.isAnalyseTimeout() || !GlobalConfiguration.IS_NEED_ADD_TO_TIMEOUT_LIST) {
                // check OOM
//                methodReference.setContainsOutOfMemOptions(context.isContainsOutOfMemOptions());
                methodReference.setInitialed(true);
                methodReference.setActionInitialed(true);
            }
        }
    }

    public static void analyse(String uuid, SootMethod method, MethodReference methodRef, DataContainer dataContainer) {
        try (Context context = Context.newInstance(methodRef.getSignature(), methodRef, dataContainer)) {
            runningMethods.put(uuid, context);
            CachedPointerAnalysis.processMethod(method, context);
        }
    }

    /**
     * 返回return value
     *
     * @param method
     * @param context
     * @return
     */
    public static boolean processMethod(SootMethod method, Context context) {
        String signature = "";
        MethodReference methodReference = context.getMethodReference();
        if (methodReference.isBodyParseError()) return false;

        int maxSleepTimes = 5;
        while (methodReference.isRunning() && maxSleepTimes > 0) {
            // 如果已经有线程在运行了，随机睡几秒
            int random = (int) (Math.random() * 20);
            try {
                Thread.sleep(random);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            // 如果睡完，已经分析完了，则直接返回
            if (methodReference.isActionInitialed()) {
                return true;
            }
            maxSleepTimes--;
        }

        try {
            signature = methodReference.getSignature();
            Body body = SemanticUtils.retrieveBody(method, signature, true);

            if (body != null
                    && body.getUnits().getModificationCount() >= GlobalConfiguration.METHOD_MAX_BODY_COUNT) {
                // 超级长的函数 不分析，可能会发生内存泄露的问题
                log.debug("Method {} body is too big, ignore", signature);
                methodReference.setInitialed(true);
                methodReference.setActionInitialed(true);
                return false;
            }

            if (body != null) {
                methodReference.setRunning(true);
                UnitGraph graph = new BriefUnitGraph(body);
                CachedPointerAnalysis.makeDefault(graph, context);
                return true;
            }
        } catch (Exception e) {
            String msg = e.getMessage();
            if (msg != null && msg.contains("Body retrieve error: ")) {
                methodReference.setBodyParseError(true);
                log.warn(msg);
            } else {
                log.error(msg);
                e.printStackTrace();
            }
        } finally {
            methodReference.setRunning(false);
        }

        return false;
    }


    /**
     * 测试debug用
     *
     * @param o1
     * @param o2
     */
    public static void diff(ValueContainer o1, ValueContainer o2) {
        System.out.println("var table");
        debugPrintNodeRefs(o1.getLocalVarTable(), o2.getLocalVarTable());
//        debugPrintNodeRefs(o1.getGlobalVarTable(), o2.getGlobalVarTable());
        System.out.println("nodes");
        o1.getCache().invalidateAll();
        o2.getCache().invalidateAll();
        debugPrint(o1.getNodeTable(), o2.getNodeTable());
    }

    public static void debugPrintNodeRefs(Multimap<String, NodeRef> o1, Multimap<String, NodeRef> o2) {
        Set<String> visited = new HashSet<>();
        Set<String> keys = o1.keySet();
        for (String key : keys) {
            if(visited.contains(key)) return;
            visited.add(key);
            Collection<NodeRef> o1Refs = o1.get(key);
            Collection<NodeRef> o2Refs = o2.get(key);
            String o1Value = String.format("[%s]%s", key, o1Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            String o2Value = String.format("[%s]%s", key, o2Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            if(!o1Value.equals(o2Value)) {
                System.out.printf("in%s%n", o1Value);
                System.out.printf("out%s%n", o2Value);
            }
        }
        keys = o2.keySet();
        for (String key : keys) {
            if(visited.contains(key)) continue;
            visited.add(key);
            Collection<NodeRef> o1Refs = o1.get(key);
            Collection<NodeRef> o2Refs = o2.get(key);
            String o1Value = String.format("[%s]%s", key, o1Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            String o2Value = String.format("[%s]%s", key, o2Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            if(!o1Value.equals(o2Value)) {
                System.out.printf("in%s%n", o1Value);
                System.out.printf("out%s%n", o2Value);
            }
        }
        visited.clear();
    }

    public static void debugPrint(Multimap<String, String> o1, Multimap<String, String> o2) {
        Set<String> visited = new HashSet<>();
        Set<String> keys = o1.keySet();

        for (String key : keys) {
            if(visited.contains(key)) continue;
            visited.add(key);
            Collection<String> o1Refs = o1.get(key);
            Collection<String> o2Refs = o2.get(key);
            String o1Value = String.format("[%s]%s", key, o1Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            String o2Value = String.format("[%s]%s", key, o2Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            if(!o1Value.equals(o2Value)) {
                System.out.printf("in%s%n", o1Value);
                System.out.printf("out%s%n", o2Value);
            }
        }
        keys = o2.keySet();
        for (String key : keys) {
            if(visited.contains(key)) continue;
            visited.add(key);
            Collection<String> o1Refs = o1.get(key);
            Collection<String> o2Refs = o2.get(key);
            String o1Value = String.format("[%s]%s", key, o1Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            String o2Value = String.format("[%s]%s", key, o2Refs.stream().map(Objects::toString).collect(Collectors.toSet()));
            if(!o1Value.equals(o2Value)) {
                System.out.printf("in%s%n", o1Value);
                System.out.printf("out%s%n", o2Value);
            }
        }

        visited.clear();
    }

    @Override
    public void close() {
        context = null;
        retValue = null;
        stmtSwitcher = null;
        triggerTimes.clear();
    }

    public static void stopAll(){
        for(MethodContext methodContext : runningMethods.values()){
            methodContext.setAnalyseTimeout(true);
        }
    }
}
