package tabby.analysis.v2.container;


import com.google.common.cache.*;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import com.google.common.collect.MultimapBuilder;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import soot.Local;
import soot.SootField;
import soot.Value;
import soot.ValueBox;
import soot.jimple.ArrayRef;
import soot.jimple.InstanceFieldRef;
import tabby.analysis.v2.data.Taint;
import tabby.analysis.v2.data.actions.Action;
import tabby.analysis.v2.data.nodes.*;
import tabby.analysis.v2.switcher.NodeSwitcher;
import tabby.common.utils.PositionUtils;
import tabby.common.utils.SemanticUtils;
import tabby.core.container.DataContainer;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * @project tabby_ng
 * @since 2025/4/1
 */
@Getter
@Setter
@Slf4j
public class ValueContainer implements AutoCloseable {

    // key 为变量名，如a，不会出现a.f这种类型，数组类型处理成 b[0] = c -> key 为 b
    // 只有base
    private Multimap<String, NodeRef> localVarTable = MultimapBuilder.hashKeys().hashSetValues().build(); // var -> nodes
    private Multimap<String, NodeRef> globalVarTable = MultimapBuilder.hashKeys().hashSetValues().build(); // var -> nodes
    private boolean saveAnyway = false;
    private Multimap<String, String> nodeTable = MultimapBuilder.hashKeys().hashSetValues().build(); // uid -> serialized，当前分析语句结束分析后更新，正常情况下，当前变量分析过程中是不变的
    private LoadingCache<String, Node> cache
            = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .removalListener(new RemovalListener<String, Node>() {
                @Override
                public void onRemoval(RemovalNotification<String, Node> notification) {
                    String uid = notification.getKey();
                    Node node = notification.getValue();
                    if(node == null) return;
                    
                    if(saveAnyway || node.isModified()){
                        if(nodeTable.containsKey(uid)){
                            nodeTable.replaceValues(uid, node.serialize());
                        }else{
                            nodeTable.putAll(uid, node.serialize());
                        }
                    }
                }
            })
            .build(new CacheLoader<>() {
                @Override
                public Node load(String key) throws ExecutionException{
                    if(nodeTable.containsKey(key)){
                        Collection<String> serialized = nodeTable.get(key);
                        return Node.deserialize(serialized);
                    }

                    throw new ExecutionException(key + " Not Found", null);
                }
            }); // uid:Node



    private String[] objects = null; // 临时的列表
    private DataContainer dataContainer;

    private NodeSwitcher nodeSwitcher = null; // 每个container都有一个私有的NodeSwitcher

    public ValueContainer() {
    }

    public ValueContainer(DataContainer dataContainer) {
        this();
        this.dataContainer = dataContainer;
        this.nodeSwitcher = new NodeSwitcher();
        this.nodeSwitcher.setContainer(this);
    }

    public ValueContainer(Multimap<String, Multimap<String, String>> summaries, DataContainer dataContainer) {
        this(dataContainer);

        summaries.forEach((var, values) -> {
            Set<String> uids = values.keySet();
            for (String uid : uids) {
                Collection<String> serialized = values.get(uid);
                if(serialized.isEmpty()) continue;
                Node node = Node.deserialize(serialized);
                save(node);
                if(uid.startsWith("H")){
                    NodeRef nodeRef = node.makeRef();
                    if(nodeRef.isStatic()){
                        globalVarTable.put(var, nodeRef);
                    }else{
                        localVarTable.put(var, nodeRef);
                    }
                }
            }
        });
    }

    /**
     * 常见具有相同global参数的value域
     * @return
     */
    public ValueContainer createSubContainer() {
        ValueContainer container = new ValueContainer();
        container.setGlobalVarTable(globalVarTable);
        Set<Node> allNodes = new HashSet<>();
        for(String key:globalVarTable.keys()){
            Set<Node> nodes = getAllRelatedNodesByVarName(key, false);
            allNodes.addAll(nodes);
        }
        container.save(allNodes);
        container.setDataContainer(dataContainer);
        container.cache.invalidateAll(); //  fresh and save
        return container;
    }

    public Node getNode(Value value){
        NodeRef ref = NodeRef.of(value);
        return getNode(ref);
    }

    public Set<NodeRef> getNodeRefsByAction(String action, String[] callSiteVarNames){
        Action act = Action.of(action, callSiteVarNames);
        return act.getRefs(null, true, this);
    }

    public Set<Node> getNodesByAction(String action, String[] callSiteVarNames){
        Action act = Action.of(action, callSiteVarNames);
        return act.getNodes((String) null, true,this);
    }

    public Set<Node> getNodesByNodeRefs(Set<NodeRef> refs){
        Set<Node> nodes = new HashSet<>();
        for(NodeRef ref : refs){
            Node node = getNode(ref);
            if(node != null) nodes.add(node);
        }
        return nodes;
    }

    public Node getNode(NodeRef ref) {
        Node ret = null;

        try { // get from globalNodes
            return cache.get(ref.getUid());
        } catch (ExecutionException e) {
            // ignore
        }

        return ret;
    }

    public Node getNode(String uid) {
        Node ret = null;

        try { // get from globalNodes
            return cache.get(uid);
        } catch (ExecutionException e) {
            // ignore
        }

        return ret;
    }

    public Set<Node> getNodes(Value value){
        Set<Node> ret = new HashSet<>();
        Collection<NodeRef> refs = getVarNodeRefs(value);
        for(NodeRef ref : refs){
            Node node = getNode(ref);
            if(node != null) ret.add(node);
        }
        return ret;
    }

    public Collection<NodeRef> getVarNodeRefs(Value value){
        if(value instanceof InstanceFieldRef fieldRef){
            SootField field = fieldRef.getField();
            Value base = fieldRef.getBase();
            Collection<NodeRef> refs = getVarNodeRefs(base);
            if(refs.isEmpty()) return new ArrayList<>();
            Collection<NodeRef> ret = new ArrayList<>(refs);
            for(NodeRef ref : refs){
                Node node = getNode(ref);
                if(node != null) {
                    ret.addAll(node.getFields().get(field.getName()));
                }
            }
            return ret;
        }else if(value instanceof ArrayRef){
            String var = SemanticUtils.extractVarName(value);
            Collection<NodeRef> refs = localVarTable.get(var);
            if(refs.isEmpty()){
                refs = globalVarTable.get(var);
            }
            Collection<NodeRef> ret = new ArrayList<>(refs);
            for(NodeRef ref : refs){
                Node node = getNode(ref);
                if(node instanceof ArrayNode an) {
                    ret.addAll(an.getArray());
                }
            }
            return ret;
        } else {
            String var = SemanticUtils.extractVarName(value);
            Collection<NodeRef> ret = localVarTable.get(var);
            if(ret.isEmpty()){
                ret = globalVarTable.get(var);
            }
            return ret;
        }
    }

    public boolean isThisObjField(Value value) {
        Collection<NodeRef> refs = getVarNodeRefs(value);
        if(refs.isEmpty()) return false;
        for(NodeRef ref : refs){
            Node node = getNode(ref);
            if(node != null && node.isThisFieldObj()){
                return true;
            }
        }
        return false;
    }

    public boolean isContainsTainted(Value value) {
        Set<Integer> positions = getTaintIdsByValue(value);
        if(positions.isEmpty()) return false;
        if(positions.size() == 1 && positions.contains(PositionUtils.NOT_POLLUTED_POSITION)) return false;
        return true;
    }

    public boolean isContainsTainted(Node node) {
        Set<Integer> positions = new HashSet<>();
        getTaintIdsByNode(node, new HashSet<>(), positions);
        if(positions.isEmpty()) return false;
        if(positions.size() == 1 && positions.contains(PositionUtils.NOT_POLLUTED_POSITION)) return false;
        return true;
    }

    public Set<String> getVarNodeTypes(Value value){
        Set<String> ret = Sets.newConcurrentHashSet();
        Collection<NodeRef> refs = getVarNodeRefs(value);
        if(refs.isEmpty()) return ret;
        for(NodeRef ref : refs){
            if(ref != null && ref.getType() != null){
                ret.add(ref.getType());
            }
        }
        if(ret.isEmpty()){
            ret.add("java.lang.Object");
        }
        return ret;
    }

    public Set<Integer> getTaintIdsByValueBoxes(List<ValueBox> boxes){
        Set<Integer> ret = new HashSet<>();
        for(ValueBox vb : boxes){
            Value value = vb.getValue();
            if(value != null){
                ret.addAll(getTaintIdsByValue(value));
            }
        }
        return ret;
    }

    public Set<Integer> getTaintIdsByVarName(String varName){
        Set<Integer> ret = new HashSet<>();
        Set<Node> nodes = getNodesByVarName(varName);
        if(nodes.isEmpty()) return ret;
        Set<String> visited = new HashSet<>();
        for(Node node : nodes){
            getTaintIdsByNode(node, visited, ret);
        }
        return ret;
    }

    public Set<Integer> getTaintIdsByNodeRefs(Set<NodeRef> refs){
        Set<Integer> ret = new HashSet<>();
        Set<String> visited = new HashSet<>();
        for(NodeRef ref : refs){
            Node node = getNode(ref);
            if(node != null){
                getTaintIdsByNode(node, visited, ret);
            }
        }
        return ret;
    }

    public Set<Integer> getTaintIdsByNodes(Set<Node> nodes){
        Set<Integer> ret = new HashSet<>();
        Set<String> visited = new HashSet<>();
        for(Node node : nodes){
            if(node != null){
                getTaintIdsByNode(node, visited, ret);
            }
        }
        return ret;
    }

    public Set<Integer> getTaintIdsByValue(Value value){
        Set<Integer> ret = new HashSet<>();
        Collection<NodeRef> refs = getVarNodeRefs(value);
        if(refs.isEmpty()) return ret;
        for(NodeRef ref : refs){
            Node node = getNode(ref);
            getTaintIdsByNode(node, new HashSet<>(), ret);
        }
        if(ret.isEmpty()){
            ret.add(PositionUtils.NOT_POLLUTED_POSITION);
        }
        return ret;
    }

    public Set<Integer> getTaintIdsByNode(Node node){
        Set<Integer> ret = new HashSet<>();
        getTaintIdsByNode(node, new HashSet<>(), ret);
        return ret;
    }

    public void getTaintIdsByNode(Node node, Set<String> visited, Set<Integer> taints){
        if(node == null || node instanceof NullNode) return ;
        if(visited.contains(node.getUid())){
            return ;
        }
        visited.add(node.getUid());
        taints.addAll(node.getTaintIds());

        if(node instanceof ConstantNode) return;

        // fields
        node.getFields().forEach((fieldName, fieldRef) -> {
            Node fieldNode = getNode(fieldRef);
            getTaintIdsByNode(fieldNode, visited, taints);
        });
        // special node refs
        if(node instanceof ArrayNode arrayNode){
            arrayNode.getArray().forEach(nodeRef -> {
                Node a = getNode(nodeRef);
                getTaintIdsByNode(a, visited, taints);
            });
        }else if(node instanceof MapNode mapNode){
            mapNode.getKeys().forEach(nodeRef -> {
                Node k = getNode(nodeRef);
                getTaintIdsByNode(k, visited, taints);
            });
            mapNode.getValues().forEach(nodeRef -> {
                Node v = getNode(nodeRef);
                getTaintIdsByNode(v, visited, taints);
            });
        }
    }

    public Set<NodeRef> removeNodeRefsByVarName(String varName){
        if(varName ==null || varName.isEmpty()) return new HashSet<>();
        Set<NodeRef> refs = new HashSet<>(localVarTable.removeAll(varName));
        if(refs.isEmpty()) {
            refs.addAll(globalVarTable.removeAll(varName));
        }
        return refs;
    }

    public Set<NodeRef> getNodeRefsByVarName(String varName){
        if(varName ==null || varName.isEmpty()) return new HashSet<>();
        Set<NodeRef> refs = new HashSet<>(localVarTable.get(varName));
        if(refs.isEmpty()) {
            refs.addAll(globalVarTable.get(varName));
        }
        return refs;
    }

    public Set<NodeRef> getNodeRefsByVarNames(String... names){
        for(String name : names){
            if(name == null) continue;
            Set<NodeRef> refs = getNodeRefsByVarName(name);
            if(!refs.isEmpty()) return refs;
        }
        return new HashSet<>();
    }

    public Set<Node> getNodesByVarName(String name){
        Set<NodeRef> refs = getNodeRefsByVarName(name);
        Set<Node> ret = new HashSet<>();
        for(NodeRef ref : refs){
            Node node = getNode(ref);
            if(node != null) {
                ret.add(node);
            }
        }
        return ret;
    }

    public Set<Node> getNodesByVarNames(String... names){
        for(String name : names){
            if(name == null) continue;
            Set<Node> nodes = getNodesByVarName(name);
            if(!nodes.isEmpty()) return nodes;
        }
        return new HashSet<>();
    }

    public Set<Node> removeNodesByVarName(String name){
        Set<NodeRef> refs = removeNodeRefsByVarName(name);
        Set<Node> ret = new HashSet<>();
        for(NodeRef ref : refs){
            Node node = getNode(ref);
            if(node != null) {
                ret.add(node);
            }
        }
        return ret;
    }

    public ValueContainer createContainerWithInitialVars(String[] vars){
        Multimap<String, NodeRef> copiedLocalVarTable = MultimapBuilder.hashKeys().hashSetValues().build();
        Multimap<String, NodeRef> copiedGlobalVarTable = MultimapBuilder.hashKeys().hashSetValues().build();
        Multimap<String, String> copiedNodesTable = MultimapBuilder.hashKeys().hashSetValues().build();
        for(String var : vars){
            if(var == null) continue;
            Collection<NodeRef> refs = localVarTable.get(var);
            if(refs.isEmpty()){
                refs = globalVarTable.get(var);
                copiedGlobalVarTable.putAll(var, new HashSet<>(refs));
            }else{
                copiedLocalVarTable.putAll(var, new HashSet<>(refs));
            }
            for(NodeRef ref : refs){
                Node node = getNode(ref);
                Set<Node> related = getAllRelatedNodes(node, false);
                for(Node relatedNode : related){
                    copiedNodesTable.putAll(relatedNode.getUid(), relatedNode.serialize());
                }
            }
        }
        ValueContainer valueContainer = new ValueContainer(dataContainer);
        valueContainer.localVarTable = copiedLocalVarTable;
        valueContainer.globalVarTable = copiedGlobalVarTable;
        valueContainer.nodeTable = copiedNodesTable;
        return valueContainer;
    }

    /**
     * 深度优先搜索 node 相关的所有node
     * @param node
     * @return
     */
    public Set<Node> getAllRelatedNodes(Node node, boolean isRemoveUntainted) {
        Set<Node> visited = Sets.newConcurrentHashSet();
        deepFirstSearchNodes(node, visited, isRemoveUntainted);
        return visited;
    }

    public Set<Node> getAllRelatedNodesByVarName(String var, boolean isRemoveUntainted) {
        Set<Node> visited = Sets.newConcurrentHashSet();
        Set<Node> nodes = getNodesByVarName(var);
        for(Node node : nodes){
            deepFirstSearchNodes(node, visited, isRemoveUntainted);
        }
        return visited;
    }

    private void deepFirstSearchNodes(Node node, Set<Node> visited, boolean isRemoveUntainted) {
        if(node == null || visited.contains(node)) {
            return;
        }

        visited.add(node);
        if(node.isArray()){
            ArrayNode arrayNode = (ArrayNode) node;
            if(isRemoveUntainted && !Taint.isContainsTaint(node.getTaints(), null) && arrayNode.getArray().isEmpty()){
                visited.remove(node);
            }

            if(!arrayNode.getArray().isEmpty()){
                arrayNode.getArray().forEach((nodeRef) -> {
                    Node a = getNode(nodeRef);
                    deepFirstSearchNodes(a, visited, isRemoveUntainted);
                });
            }
        }else if(node.isConstant() || node.isNull()){
            // pass
            if(isRemoveUntainted && !Taint.isContainsTaint(node.getTaints(), null)){
                visited.remove(node); // 不保存不可控的node
            }
        }else if(node.isMap()){
            MapNode mapNode = (MapNode) node;
            if(isRemoveUntainted && !Taint.isContainsTaint(node.getTaints(), null)
                    && mapNode.getKeys().isEmpty() && mapNode.getValues().isEmpty()){
                visited.remove(node);
            }

            mapNode.getKeys().forEach((nodeRef) -> {
                Node a = getNode(nodeRef);
                deepFirstSearchNodes(a, visited, isRemoveUntainted);
            });
            mapNode.getValues().forEach((nodeRef) -> {
                Node a = getNode(nodeRef);
                deepFirstSearchNodes(a, visited, isRemoveUntainted);
            });
        } else if(node instanceof FakeNode fn){
            if(isRemoveUntainted && !Taint.isContainsTaint(fn.getTaints(), null)){
                visited.remove(node);
            }
        } else {
            if(isRemoveUntainted && !Taint.isContainsTaint(node.getTaints(), null) && node.getFields().isEmpty()){
                visited.remove(node);
            }

            node.getFields().forEach((fieldName, nodeRef) -> {
                Node a = getNode(nodeRef);
                deepFirstSearchNodes(a, visited, isRemoveUntainted);
            });
        }
    }

    public Set<Node> getOrCreateNode(Value value) {
        nodeSwitcher.setType(NodeSwitcher.GET_OR_CREATE_NODE);
        value.apply(nodeSwitcher);
        return (Set<Node>) nodeSwitcher.getResult(); // created a new node.
    }

    public Node createNode(Local v){
        Node node = Node.ofNodeWithValue(v);
        node.setName(v.getName());
        node.getTaints().add(Taint.of(PositionUtils.NOT_POLLUTED_POSITION, v.getName()));
        save(node);
        relateToVarTablesWithRemoval(SemanticUtils.extractVarName(v), node);
        return node;
    }

    public void save(Node node) {
        // 只要调用了save，那node肯定是被修改了的
        // 没修改的不用保存到nodeTable
        node.setModified(true);
        cache.put(node.getUid(), node);
    }

    public void saveAndFresh(Node node) {
        cache.invalidate(node.getUid());
        nodeTable.replaceValues(node.getUid(), node.serialize());
    }

    public void saveIfAbsent(Node node) {
        cache.invalidate(node.getUid());
        if(!nodeTable.containsKey(node.getUid())){
            nodeTable.putAll(node.getUid(), node.serialize());
        }else if(node.isFake()){
            // merge
            Node inTable = getNode(node.getUid());
            inTable.getTaints().addAll(node.getTaints());
            save(inTable);
        }else if(node.isArray() && SemanticUtils.isPrimType(node.getType()) && Taint.isContainsTaint(node.getTaints(), null)){
            // contant array 复制一份
            // 对于非污点的，copy一份也不会有影响
            String newUid = node.getUid(); // 只允许新增一个uid_0
            if(!node.getUid().contains("_")){
                newUid = node.getUid() + "_0";
            }
            if(nodeTable.containsKey(newUid)){
                Node inTable = getNode(node.getUid());
                inTable.getTaints().addAll(node.getTaints());
                save(inTable);
            }else{
                node.setUid(newUid);
                nodeTable.replaceValues(node.getUid(), node.serialize());
            }
        }
    }

    public void save(Set<Node> nodes) {
        for(Node node : nodes){
            if(node != null){
                node.setModified(true);
                cache.put(node.getUid(), node);
            }
        }
    }

    public void delete(Node node) {
        cache.invalidate(node.getUid());
        nodeTable.removeAll(node.getUid());
    }

    public void assign(String varName, Node node, boolean isStatic, boolean isRemoval){
        if(node == null){
            if(isStatic){
                globalVarTable.removeAll(varName);
            }else{
                localVarTable.removeAll(varName);
            }
            return;
        }

        if(isStatic){
            if(node.getUid().startsWith("O") && globalVarTable.containsKey(varName)) return;

            if(isRemoval){
                globalVarTable.replaceValues(varName, Collections.singletonList(node.makeRef()));
            }else{
                globalVarTable.put(varName, node.makeRef());
            }
        }else{
            if(node.getUid().startsWith("O") && localVarTable.containsKey(varName)) return;

            if(isRemoval){
                localVarTable.replaceValues(varName, Collections.singletonList(node.makeRef()));
            }else{
                localVarTable.put(varName, node.makeRef());
            }
        }
    }

    public void assign(String varName, Set<Node> nodes, boolean isStatic, boolean isRemoval){
        if(nodes == null){
            if(isStatic){
                globalVarTable.removeAll(varName);
            }else{
                localVarTable.removeAll(varName);
            }
            return;
        }

        if(nodes.isEmpty()) return;
        Set<NodeRef> refs = nodes.stream().map(Node::makeRef).collect(Collectors.toSet());
        if(refs.size() > 1){
            refs.remove(NullNode.INSTANCE_REF); // 有多个结果时，移除null
        }
        if(isStatic){
            if(isRemoval){
                globalVarTable.replaceValues(varName, refs);
            }else{
                globalVarTable.putAll(varName, refs);
            }
        }else{
            if(isRemoval){
                localVarTable.replaceValues(varName, refs);
            }else{
                localVarTable.putAll(varName, refs);
            }
        }
    }

    public void relateToVarTablesWithoutRemoval(String var, Node node) {
        if(node == null) return;

        if(node.isStatic()){
            if(node.getUid().startsWith("O") && globalVarTable.containsKey(var)) return;
            globalVarTable.put(var, node.makeRef());
        }else{
            if(node.getUid().startsWith("O") && localVarTable.containsKey(var)) return;
            localVarTable.put(var, node.makeRef());
        }
    }

    public void relateToVarTables(String var, Set<NodeRef> refs, boolean isNeedRemove) {
        if(refs == null || refs.isEmpty()) return;

        if(refs.size() > 1){
            refs.remove(NullNode.INSTANCE_REF); // 有多个结果时，移除null
        }

        if(isNeedRemove){
            if(localVarTable.containsKey(var)){
                localVarTable.replaceValues(var, refs);
            }else if(globalVarTable.containsKey(var)){
                globalVarTable.replaceValues(var, refs);
            }else{
                localVarTable.putAll(var, refs);
            }
        }else{
            if(localVarTable.containsKey(var)){
                localVarTable.putAll(var, refs);
            }else if(globalVarTable.containsKey(var)){
                globalVarTable.putAll(var, refs);
            }else{
                localVarTable.putAll(var, refs);
            }
        }

    }

    public void relateToVarTablesWithRemoval(String var, Node node) {
        if(node == null){ // delete all
            localVarTable.removeAll(var);
            globalVarTable.removeAll(var);
            return;
        }

        if(node.isStatic()){
            globalVarTable.replaceValues(var, Collections.singletonList(node.makeRef()));
        }else{
            localVarTable.replaceValues(var, Collections.singletonList(node.makeRef()));
        }
    }

    public void relateToVarTablesWithRemoval(String var, Set<Node> nodes) {
        if(nodes == null){ // delete all
            localVarTable.removeAll(var);
            globalVarTable.removeAll(var);
            return;
        }

        boolean first = true;
        for(Node node : nodes){
            if(first){
                relateToVarTablesWithRemoval(var, node);
                first = false;
            }else{
                relateToVarTablesWithoutRemoval(var, node);
            }
        }
    }

    public void copy(ValueContainer source) {
        cache.invalidateAll(); // 防止cache影响被替换后的内容
        source.getCache().invalidateAll(); // 保证cache里的内容已经缓存到了nodes
        localVarTable = HashMultimap.create(source.localVarTable);
        globalVarTable = HashMultimap.create(source.globalVarTable);

        nodeTable = HashMultimap.create(source.nodeTable);

        dataContainer = source.dataContainer;
    }

    public Multimap<String, Multimap<String, String>> generateSummaries(){
        Multimap<String, Multimap<String, String>> summaries =
                MultimapBuilder.hashKeys().hashSetValues().build();
        localVarTable.forEach((var, ref) -> {
            if(var.equals("return") || var.equals("this") || var.startsWith("param-")){
                Node node = getNode(ref);
                if(node == null) return;
                summaries.putAll(generateSummary(var, Arrays.asList(node)));
            }
        });
        return summaries;
    }

    public void union(ValueContainer other) {
        other.setSaveAnyway(true);
        other.getCache().invalidateAll(); // 保证cache里的内容已经缓存到了nodes
        other.setSaveAnyway(false);
        unionVarTable(localVarTable, other.localVarTable, other);
        unionVarTable(globalVarTable, other.globalVarTable, other);
        saveAnyway = true;
        cache.invalidateAll();
        saveAnyway = false;
        // TODO 可以尝试合并varTable下面同一个var，type相同的nodeRef
//        nodeTable.putAll(other.nodeTable);
    }

    /**
     * 优先信任other的值，如果碰到相同的内容，将当前container的值改成other里面的内容
     * @param other
     */
    public void replace(ValueContainer other){
        other.getCache().invalidateAll();
        cache.invalidateAll();
        replaceVarTable(localVarTable, other.localVarTable);
        replaceVarTable(globalVarTable, other.globalVarTable);
        replaceNodeTable(other.getNodeTable());
    }

    private void replaceNodeTable(Multimap<String, String> otherNodeTable){
        Set<String> keys = otherNodeTable.keySet();
        for(String key : keys){
            if(key.startsWith("return")) continue;
            Collection<String> otherRefs = otherNodeTable.get(key);
            if(otherRefs.isEmpty()) continue;

            if(nodeTable.containsKey(key)){
                nodeTable.replaceValues(key, otherRefs);
            }else{
                nodeTable.putAll(key, otherRefs);
            }
        }
    }

    private void replaceVarTable(Multimap<String, NodeRef> one, Multimap<String, NodeRef> two){
        Set<String> keys = two.keySet();
        for(String key : keys){
            Collection<NodeRef> twoValues = two.get(key);
            if(twoValues.isEmpty()) continue;

            if(one.containsKey(key)){
                one.replaceValues(key, twoValues);
            }else{
                one.putAll(key, twoValues);
            }
        }
    }

    private void unionVarTable(
            Multimap<String, NodeRef> one, Multimap<String, NodeRef> two,
            ValueContainer otherContainer) {
        Set<String> keys = two.keySet();
        Set<String> visited = new HashSet<>();
        for(String key : keys){
            Collection<NodeRef> oneValues = one.get(key);
            Collection<NodeRef> twoValues = two.get(key);

            if(oneValues.isEmpty() && twoValues.isEmpty()) continue;

            if(oneValues.isEmpty()){
                one.putAll(key, twoValues);
                // 将other nodes搬到this container
                for(NodeRef twoValue : twoValues){
                    copyNodeFromOtherContainer(twoValue, visited, otherContainer);
                }
            }else if(twoValues.isEmpty()){
                // do nothing
            }else{
                for(NodeRef twoValue : twoValues){
                    if(oneValues.contains(twoValue)){
                        // union nodes
                        Node oneNode = getNode(twoValue);
                        Node twoNode = otherContainer.getNode(twoValue);
                        if(oneNode != null && twoNode != null){
                            oneNode.union(twoNode, visited, true, this, otherContainer);
                        }else if(twoNode != null){
                            copyNodeFromOtherContainer(twoNode.makeRef(), visited, otherContainer);
                        }
                    }else{
                        one.put(key, twoValue);
                        copyNodeFromOtherContainer(twoValue, visited, otherContainer);
                    }
                }
            }
        }
    }

    public void copyNodeFromOtherContainer(NodeRef other, Set<String> visited, ValueContainer otherContainer) {
        if(visited.contains(other.getUid())) return;

        visited.add(other.getUid());
        Node node = otherContainer.getNode(other);
        Set<Node> nodes = otherContainer.getAllRelatedNodes(node, false);
        for(Node otherNode : nodes){
            Node curNode = getNode(otherNode.getUid());
            if(curNode != null){
                curNode.union(otherNode, visited,
                        true, this, otherContainer);
            }else{
                save(otherNode);
            }
        }
    }

    @Override
    public void close() throws Exception {
        cache.invalidateAll(); // 强制移除所有cache，会触发removalListener更新Nodes
    }

    public Multimap<String, Multimap<String, String>> generateSummary(String key, Collection<Node> nodes){
        Multimap<String, Multimap<String, String>> summary = MultimapBuilder.hashKeys().hashSetValues().build();
        for(Node node: nodes){
            if(node != null){
                if(!isContainsTainted(node)) continue;
                Multimap<String, String> tempLocalNodes = MultimapBuilder.hashKeys().hashSetValues().build();
                Set<Node> allNodes = getAllRelatedNodes(node, false);
                allNodes.remove(node);
                Node copied = node.clone();
                copied.setUid("H"+copied.getUid());
                tempLocalNodes.putAll(copied.getUid(), copied.serialize());

                allNodes.forEach(related -> {
                    tempLocalNodes.putAll(related.getUid(), related.serialize());
                });

                if(!tempLocalNodes.isEmpty()){
                    summary.put(key, tempLocalNodes);
                }
            }
        }
        return summary;
    }



    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (!(o instanceof ValueContainer container)) return false;

        return new EqualsBuilder()
                .append(localVarTable, container.localVarTable)
                .append(globalVarTable, container.globalVarTable)
                .append(nodeTable, container.nodeTable).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(localVarTable).append(globalVarTable)
                .append(nodeTable).toHashCode();
    }
}
