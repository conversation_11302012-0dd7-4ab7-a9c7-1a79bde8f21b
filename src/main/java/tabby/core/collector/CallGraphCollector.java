package tabby.core.collector;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import soot.SootMethod;
import tabby.analysis.v1.SimpleTypeAnalysis;
import tabby.analysis.v2.CachedPointerAnalysis;
import tabby.common.bean.ref.MethodReference;
import tabby.common.utils.SemanticUtils;
import tabby.common.utils.TickTock;
import tabby.config.GlobalConfiguration;
import tabby.core.container.DataContainer;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2021/4/23
 */
@Slf4j
@Service
@Setter
public class CallGraphCollector {

    public CallGraphCollector() {
    }

    /**
     * 全量调用图生成
     */
    @Async("tabby-collector")
    public void collect(MethodReference methodRef, DataContainer dataContainer, TickTock tickTock) {
        String uuid = UUID.randomUUID().toString();
        try {
            if (methodRef.isIgnore() || methodRef.isInitialed() || methodRef.isBodyParseError()) {
                tickTock.countDown();
                return; // 消除后续的调用边 和 重复分析的情况
            }

            SootMethod method = methodRef.getMethod();
            if (method == null) {
                tickTock.countDown();
                return; // 提取不出内容，不分析
            }

            if (method.isStatic() && method.getParameterCount() == 0 && !GlobalConfiguration.IS_NEED_ANALYSIS_EVERYTHING) {
                // 静态函数 且 函数入参数量为0
                // 此类函数里的函数调用一般均无法可控，就算后续出现了sink函数，我们也没办法利用
                tickTock.countDown();
                return;
            }

            if (method.isAbstract()
                    || method.isNative()
                    || method.isPhantom()) {
                tickTock.countDown();
                return;
            }

            if (methodRef.isDao() || methodRef.isRpc() || methodRef.isMRpc()) {
                // 对于上面3种类型，不进行分析，直接赋予action
                if("CachedPointerAnalysis".equals(GlobalConfiguration.POINTER_ALGORITHM_TYPE)){
                    SemanticUtils.applySpecialMethodSummary(methodRef, null);
                }else{
                    SemanticUtils.applySpecialMethodActions(methodRef);
                }
                tickTock.countDown();
                return;
            }

//            if(!methodRef.getSignature().equals("<java.util.regex.Pattern: java.util.regex.Pattern$Node expr(java.util.regex.Pattern$Node)>")) {
//                tickTock.countDown();
//                return;
//            }

            log.debug(method.getSignature()+" start...");
            long start = System.nanoTime();
            if(GlobalConfiguration.POINTER_ALGORITHM_TYPE.equals("CachedPointerAnalysis")){
                CachedPointerAnalysis.analyse(uuid, method, methodRef, dataContainer);
            }else if(GlobalConfiguration.POINTER_ALGORITHM_TYPE.equals("SimpleTypeAnalysis")){
                SimpleTypeAnalysis.analyse(uuid, method, methodRef, dataContainer);
            }
            log.debug(method.getSignature()+" end, cost: "+(TimeUnit.NANOSECONDS.toSeconds(System.nanoTime() - start))+" s.");
            tickTock.countDown(); // TODO debug
        } catch (RuntimeException e) {
            log.error("Something error on call graph. " + methodRef.getSignature());
            String msg = e.getMessage();
            log.error(msg);
            tickTock.countDown();
        } catch (OutOfMemoryError e) {
            log.error("OOM Error!!!! Force Stop Everything!!!");
            System.exit(1); // just stop
            tickTock.countDown();
            GlobalConfiguration.GLOBAL_FORCE_STOP = true;
        } catch (Exception e) {
            tickTock.countDown();
            if (e instanceof InterruptedException) {
                log.error("Thread interrupted. " + methodRef.getSignature());
                return;
            } else {
                log.error("Something error on call graph. " + methodRef.getSignature());
                e.printStackTrace();
            }
        } finally {
            if(GlobalConfiguration.POINTER_ALGORITHM_TYPE.equals("CachedPointerAnalysis")){
                CachedPointerAnalysis.runningMethods.remove(uuid);
            }else if(GlobalConfiguration.POINTER_ALGORITHM_TYPE.equals("SimpleTypeAnalysis")){
                SimpleTypeAnalysis.runningMethods.remove(uuid);
            }
        }

        log.debug("Remain {} methods.", tickTock.getCount());
    }

}
